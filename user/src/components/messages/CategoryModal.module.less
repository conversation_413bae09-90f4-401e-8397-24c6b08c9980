/* Modal Header */
.modal-header {
  display: flex;
  align-items: center;
  gap: 12px;
  position: sticky;
  top: 0;
  z-index: 2;
  background: var(--bg-modal);
  padding: 4px 0;
}

.modal-header h2 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: var(--text-color);
}

/* Container Layout */
.category-container {
  display: flex;
  flex-direction: column;
  gap: 24px;
  max-height: 73vh;
  overflow-y: auto;
  padding-right: 8px;
  scroll-behavior: smooth;
}

/* Scrollbar Styling */
.category-container::-webkit-scrollbar {
  width: 6px;
}

.category-container::-webkit-scrollbar-track {
  background: var(--bg-secondary);
  border-radius: 3px;
}

.category-container::-webkit-scrollbar-thumb {
  background: var(--border-color-base);
  border-radius: 3px;
}

.category-container::-webkit-scrollbar-thumb:hover {
  background: var(--text-color-tertiary);
}

/* Add Category Form */
.add-category {
  position: sticky;
  top: 0;
  z-index: 1;
  display: flex;
  gap: 12px;
  background: var(--bg-secondary);
  padding: 16px;
  border-radius: 12px;
  border: 1px dashed var(--border-color-base);
  transition: all 0.3s ease;
  display: flex;
}

.add-category:hover {
  border-color: var(--primary-color);
  background: var(--bg-hover);
}

/* Category List */
.category-list {
  border: none;
  border-radius: 12px;
  padding: 0;
  background: var(--component-background);
  box-shadow: var(--shadow-1);
}

/* Category Items */
.category-item {
  width: 100%;
  transition: all 0.2s ease;
}

.category-content {
  cursor: pointer;
  border-radius: 10px;
  transition: all 0.2s ease;
}

.category-content:hover {
  background: var(--bg-hover);
  transform: translateY(-1px);
}

.category-tag {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: var(--component-background);
  border: 1px solid var(--border-color-split);
  padding: 16px;
  border-radius: 10px;
  transition: all 0.2s ease;
  box-shadow: var(--shadow-1);
}

.category-tag:hover {
  border-color: var(--border-color-base);
  box-shadow: var(--shadow-2);
}

/* Category Info */
.category-info {
  display: flex;
  flex-direction: column;
  gap: 6px;
  flex-shrink: 0;
}

.category-name {
  font-weight: 600;
  font-size: 15px;
  color: var(--text-color);
  letter-spacing: -0.01em;
}

.subscriber-count {
  color: var(--text-color-secondary);
  font-size: 13px;
  background: var(--bg-secondary);
  padding: 4px 12px;
  border-radius: 16px;
  display: inline-block;
  transition: all 0.2s ease;
  width: fit-content;
}

/* Default Category Styling */
.default-category-tag {
  background: var(--primary-color-light);
  border: 1px solid var(--primary-color-fade);
  transition: all 0.3s ease;

  &:hover {
    background: var(--primary-color-fade);
    border-color: var(--primary-color);
    transform: translateY(-1px);
    box-shadow: var(--shadow-2);
  }
}

.default-category-tag .category-name {
  color: var(--primary-color);
  font-weight: 600;
}

.default-category-tag .subscriber-count {
  background: var(--primary-color-light);
  color: var(--primary-color);
  border-radius: 12px;
  padding: 2px 8px;
  font-size: 11px;
  font-weight: 600;
}

/* Action Buttons */
.action-buttons {
  opacity: 0;
  transition: all 0.2s ease;
  display: flex;
  gap: 4px;
}

.category-content:hover .action-buttons {
  opacity: 1;
}

/* Skeleton Loading */
.skeleton-content {
  width: 100%;
  padding: 16px;
}

.skeleton-tag {
  display: flex;
  gap: 12px;
  background: var(--bg-secondary);
  padding: 16px;
  border-radius: 10px;
  border: 1px solid var(--border-color-base);
}

.skeleton-name {
  width: 140px;
  height: 20px;
  background: var(--border-color-base);
  border-radius: 4px;
  animation: pulse 1.5s ease-in-out infinite;
}

.skeleton-count {
  width: 100px;
  height: 16px;
  background: var(--bg-secondary);
  border-radius: 4px;
  animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Global Ant Design Overrides */
.modalWrapper {
  :global(.category-modal .ant-modal-content) {
    border-radius: 16px;
    overflow: hidden;
    box-shadow: var(--shadow-3);
    background: var(--bg-modal) !important;
    border: 1px solid var(--border-color-base);
  }

  :global(.category-modal .ant-modal-header) {
    border-bottom: none;
    padding: 20px 24px 0;
    background: var(--bg-modal) !important;

    .ant-modal-title {
      color: var(--text-color) !important;
      font-weight: 600;
    }
  }

  :global(.category-modal .ant-modal-body) {
    padding: 20px 24px;
    background: var(--bg-modal) !important;
    color: var(--text-color) !important;
  }

  :global(.category-modal .ant-modal-close) {
    color: var(--text-color-secondary) !important;

    &:hover {
      color: var(--text-color) !important;
      background: var(--bg-hover) !important;
    }
  }

  :global(.input-icon) {
    color: var(--text-color-secondary);
  }

  :global(.ant-input-affix-wrapper) {
    border-radius: 8px;
    border-color: var(--border-color-base) !important;
    background: var(--input-background) !important;
    transition: all 0.2s ease;

    .ant-input {
      background: transparent !important;
      color: var(--text-color) !important;

      &::placeholder {
        color: var(--text-color-tertiary) !important;
      }
    }
  }

  :global(.ant-input-affix-wrapper:hover) {
    border-color: var(--primary-color) !important;
    box-shadow: 0 0 0 2px var(--primary-color-light) !important;
  }

  :global(.ant-input-affix-wrapper:focus-within) {
    border-color: var(--primary-color) !important;
    box-shadow: 0 0 0 3px var(--primary-color-light) !important;
  }

  :global(.ant-btn) {
    border-radius: 8px;
    height: 38px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 500;
    transition: all 0.2s ease;
  }

  :global(.ant-btn:hover) {
    transform: translateY(-1px);
  }

  :global(.ant-btn-primary) {
    background: var(--primary-color) !important;
    border-color: var(--primary-color) !important;
    color: var(--color-white) !important;

    &:hover {
      background: var(--primary-color-hover) !important;
      border-color: var(--primary-color-hover) !important;
    }

    &:disabled {
      background: var(--bg-disabled) !important;
      border-color: var(--border-color-base) !important;
      color: var(--text-color-disabled) !important;
    }
  }

  :global(.ant-list) {
    background: transparent !important;
  }

  :global(.ant-list-item) {
    border-bottom-color: var(--border-color-split) !important;
  }

  // Dark mode specific overrides
  :global(.ant-modal-mask) {
    background: var(--bg-overlay) !important;
  }

  // Ensure proper text contrast in dark mode
  :global(.category-modal) {
    * {
      color: var(--text-color);
    }

    .ant-modal-title {
      color: var(--text-color) !important;
    }
  }

  // Action button styling for dark mode
  :global(.ant-btn-text) {
    color: var(--text-color-secondary) !important;
    border: none !important;

    &:hover {
      color: var(--text-color) !important;
      background: var(--bg-hover) !important;
    }

    &.ant-btn-dangerous {
      color: var(--error-color) !important;

      &:hover {
        color: var(--error-color) !important;
        background: rgba(240, 65, 52, 0.1) !important;
      }
    }
  }

  // Tooltip dark mode styling
  :global(.ant-tooltip) {
    .ant-tooltip-inner {
      background: var(--bg-card) !important;
      color: var(--text-color) !important;
      border: 1px solid var(--border-color-base) !important;
      box-shadow: var(--shadow-2) !important;
    }

    .ant-tooltip-arrow {
      &::before {
        background: var(--bg-card) !important;
        border-color: var(--border-color-base) !important;
      }
    }
  }
}

/* Mobile Responsive */
@media (max-width: 480px) {
  .category-container {
    max-height: 70vh;
    padding-right: 4px;
  }

  .add-category {
    flex-direction: column;
    padding: 12px;
    position: relative;
  }

  .category-tag {
    padding: 12px;
    align-items: flex-center;
    gap: 12px;
  }

  .subscriber-count {
    font-size: 12px;
  }

  .action-buttons {
    opacity: 1;
    width: 100%;
    justify-content: flex-end;
  }

  .modalWrapper {
    :global(.ant-modal-body) {
      padding: 16px;
    }

    :global(.ant-btn) {
      width: 100%;
    }
  }
}
