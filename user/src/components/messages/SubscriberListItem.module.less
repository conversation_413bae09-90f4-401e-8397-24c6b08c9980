.subscriber-item {
  padding: 12px 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  border-radius: 6px;
  margin: 2px;
  user-select: none;

  &:hover {
    background-color: var(--bg-hover);
  }

  &.selected {
    background-color: var(--primary-color-fade);
  }

  &.select-all {
    background-color: var(--bg-secondary);
    border-bottom: 1px solid var(--border-color-split);
    margin: 0;
    border-radius: 0;
    padding-top: 16px;
    padding-bottom: 16px;

    .checkbox {
      margin-right: 12px;
    }

    .username {
      font-weight: 600;
      color: var(--text-color);
    }
  }

  &.disabled {
    opacity: 0.5;
    cursor: not-allowed;

    .checkbox {
      cursor: not-allowed;
    }
  }
}

.content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.checkbox {
  margin-right: 0;
}

.categoryInfo {
  flex: 1;
  min-width: 0;
}

.avatar {
  flex-shrink: 0;
}

.info {
  display: flex;
  flex-direction: column;
  min-width: 0;
  flex: 1;
}

.username {
  font-weight: 500;
  color: var(--text-color);
  margin-bottom: 2px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  text-transform: capitalize;
}

.categoryDetails {
  display: flex;
  align-items: flex-start;
  flex-direction: column;
  gap: 2px;
}

.categoryName {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-color);
  margin: 0;
}

.subscriberCount {
  color: var(--text-color-secondary);
  font-size: 13px;
  white-space: nowrap;
}

.avatarStack {
  display: flex;
  align-items: center;
  margin-left: auto;

  .stackedAvatar {
    width: 28px;
    height: 28px;
    border: 2px solid var(--bg-white);
    margin-left: -8px;
    transition: transform 0.2s ease;

    &:hover {
      transform: translateY(-2px);
      position: relative;
    }

    &:first-child {
      margin-left: 0;
    }
  }
}

.avatarMore {
  background: var(--bg-disabled);
  color: var(--text-color-secondary);
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 12px;
  margin-left: 4px;
  white-space: nowrap;
}

// Mobile responsive styles
@media (max-width: 480px) {
  .subscriber-item {
    padding: 10px 12px;

    &.select-all {
      position: sticky;
      top: 0;
      z-index: 10;
      background-color: var(--bg-white);
    }
  }

  .content {
    gap: 12px;
  }

  .avatarStack {
    .stackedAvatar {
      width: 24px;
      height: 24px;
      margin-left: -6px;
    }
  }

  .avatarMore {
    font-size: 11px;
    padding: 1px 6px;
  }
}
