.queueContainer {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: var(--bg-white);
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  border-bottom: 1px solid var(--border-color-split);
}

.headerLeft {
  display: flex;
  align-items: center;
  gap: 12px;
}

.title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: var(--text-color);
}

.backButton {
  color: var(--text-color-secondary);
}

.addButton {
  background-color: var(--primary-color);
  border-color: var(--primary-color);

  &:hover,
  &:focus {
    background-color: var(--primary-color-hover);
    border-color: var(--primary-color-hover);
  }
}

.headerRight {
  display: flex;
  align-items: center;
  gap: 12px;
}

.themeToggle {
  :global(.ant-btn) {
    color: var(--text-color-secondary);
    border: 1px solid var(--border-color-base);
    background: var(--bg-white);
    transition: all 0.3s ease;

    &:hover,
    &:focus {
      color: var(--primary-color);
      border-color: var(--primary-color);
      background: var(--bg-hover);
    }
  }
}

.contentContainer {
  display: flex;
  flex: 1;
  overflow: hidden;
  margin-left: 20px;
  position: relative;
  min-height: 400px;

  &.isLoading {
    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: var(--bg-overlay);
      z-index: 1;
    }
  }
}

@keyframes shimmer {
  0% {
    background-position: -468px 0;
  }
  100% {
    background-position: 468px 0;
  }
}

.loadingPlaceholder {
  animation-duration: 1.5s;
  animation-fill-mode: forwards;
  animation-iteration-count: infinite;
  animation-name: shimmer;
  animation-timing-function: linear;
  background: linear-gradient(
    to right,
    var(--bg-secondary) 8%,
    var(--bg-disabled) 18%,
    var(--bg-secondary) 33%
  );
  background-size: 800px 104px;
}

.calendarSection {
  flex: 1;
  display: flex;
  flex-direction: column;
  border-right: 1px solid var(--border-color-split);
  overflow: hidden;
}

.calendarHeader {
  padding: 16px;
  border-bottom: 1px solid var(--border-color-split);
}

.monthNavigation {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16px;
}

.statsContainer {
  display: flex;
  justify-content: center;
  gap: 24px;
  margin-bottom: 20px;
  padding: 0 16px;
}

.statItem {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border-radius: 8px;
  transition: all 0.3s ease;
  cursor: default;

  &:hover {
    transform: translateY(-2px);
  }

  .statCount {
    font-size: 18px;
    font-weight: 600;
    min-width: 24px;
    text-align: center;
  }

  .statLabel {
    font-size: 14px;
    color: var(--text-color-secondary);
  }
}

.postStat {
  background: var(--primary-color-light);
  color: var(--info-color);

  .statLabel {
    color: var(--info-color);
  }
}

.messageStat {
  background: rgba(250, 173, 20, 0.1);
  color: var(--warning-color);

  .statLabel {
    color: var(--warning-color);
  }
}

.currentMonth {
  font-size: 18px;
  font-weight: 500;
  margin: 0 16px;
  color: var(--text-color);
}

.navButton {
  color: var(--text-color-secondary);
}

.filterTabs {
  :global(.ant-tabs-nav) {
    margin-bottom: 0;
  }

  :global(.ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn) {
    color: var(--primary-color);
  }

  :global(.ant-tabs-ink-bar) {
    background-color: var(--primary-color);
  }
}

.calendar {
  flex: 1;
  overflow: auto;

  :global(.ant-picker-calendar) {
    background: transparent;
  }

  :global(.ant-picker-calendar-date) {
    height: auto;
    margin: 0;
    padding: 0;
    border: none;
  }

  :global(.ant-picker-calendar-date-value) {
    display: none;
  }

  :global(.ant-picker-cell) {
    padding: 0;
  }

  :global(.ant-picker-cell-in-view) {
    color: var(--text-color);
  }

  :global(.ant-picker-calendar-date-today) {
    .dateNumber {
      color: var(--primary-color);
      font-weight: bold;
    }
  }
}

.dateCell {
  height: 100%;
  min-height: 80px;
  padding: 8px;
  border: 1px solid var(--border-color-split);
  cursor: pointer;
  transition: all 0.2s;
  position: relative;
  background-color: var(--bg-white);

  &:hover {
    background-color: var(--bg-hover);
  }

  &.selectedDate {
    background-color: var(--primary-color-light);
    border-color: var(--primary-color);
  }

  &.hasContent {
    .dateNumber {
      font-weight: 500;
    }
  }
}

.dateNumber {
  font-size: 14px;
  margin-bottom: 4px;
  color: var(--text-color);
}

.scheduledIndicator {
  position: absolute;
  bottom: 8px;
  left: 8px;
  right: 8px;
  display: flex;
  gap: 4px;
}

.postIndicator,
.messageIndicator {
  height: 4px;
  flex: 1;
  border-radius: 2px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.postIndicator {
  background-color: var(--info-color);
}

.messageIndicator {
  background-color: var(--warning-color);
}

.indicatorCount {
  position: absolute;
  top: -14px;
  font-size: 10px;
  font-weight: 500;
  color: var(--text-color-secondary);
  background: var(--bg-overlay);
  padding: 0 4px;
  border-radius: 8px;
  line-height: 1;
}

.sidebarSection {
  width: 350px;
  display: flex;
  flex-direction: column;
  border-left: 1px solid var(--border-color-split);
  background-color: var(--bg-white);
  overflow: hidden;
}

.mobileSidebar {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  box-shadow: var(--shadow-2);
}

.sidebarHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid var(--border-color-split);

  .headerTop {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
  }
}

.sidebarDate {
  margin: 0;
  font-size: 18px;
  font-weight: 500;
  color: var(--text-color);
}

.selectedDateStats {
  display: flex;
  gap: 16px;
  margin-top: 8px;
}

.selectedStatItem {
  display: flex;
  align-items: baseline;
  gap: 4px;

  .selectedStatCount {
    font-size: 16px;
    font-weight: 600;
    color: var(--primary-color);
  }

  .selectedStatLabel {
    font-size: 13px;
    color: var(--text-color-secondary);
  }
}

.sidebarMenuButton {
  color: var(--text-color-secondary);
}

.scheduledItems {
  overflow-y: auto;
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  height: 60vh;
}

.emptyState {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  gap: 16px;
  color: var(--text-color-tertiary);
  text-align: center;
}

.scheduledItem {
  border-radius: 8px;
  padding: 16px;
  box-shadow: var(--shadow-1);
  background-color: var(--bg-card);

  &.post {
    border-left: 4px solid var(--info-color);
  }

  &.message {
    border-left: 4px solid var(--warning-color);
  }
}

.itemHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.itemTime {
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 4px;
  color: var(--text-color);
}

.itemType {
  display: flex;
  align-items: center;
  gap: 4px;
  color: var(--text-color-secondary);
  font-size: 12px;
}

.itemMenuButton {
  color: var(--text-color-secondary);
  padding: 4px;
  height: auto;
}

.itemContent {
  margin-bottom: 12px;
  word-break: break-word;
  position: relative;
  color: var(--text-color);
}

.editContentWrapper {
  position: relative;
  background: var(--bg-white);
  border-radius: 4px;
}

.editInput {
  width: 100%;
  border: 1px solid var(--border-color-base);
  border-radius: 4px;
  padding: 8px;
  font-size: 14px;
  transition: all 0.3s;
  resize: none;
  background-color: var(--bg-white);
  color: var(--text-color);

  &:hover {
    border-color: var(--primary-color);
  }

  &:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px var(--primary-color-fade);
    outline: none;
  }
}

.editActions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  margin-top: 8px;
}

.editButton {
  &[type='primary'] {
    background-color: var(--primary-color);
    border-color: var(--primary-color);

    &:hover {
      background-color: var(--primary-color-hover);
      border-color: var(--primary-color-hover);
    }
  }
}

.contentText {
  padding: 8px 0;
  min-height: 24px;
  color: var(--text-color);
}

.itemMedia {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 8px;
  margin-top: 12px;

  .mediaWrapper {
    position: relative;
    padding-top: 100%; // 1:1 Aspect ratio
    overflow: hidden;
    border-radius: 8px;
    background-color: var(--bg-disabled);
    cursor: pointer;
    transition: transform 0.2s ease;

    &:hover {
      transform: scale(1.02);
    }

    img {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    &.hasMore {
      &::after {
        content: attr(data-remaining);
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.6);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        font-weight: 600;
      }
    }
  }
}

.viewSelectedButton {
  position: absolute;
  bottom: 24px;
  right: 24px;
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  box-shadow: var(--shadow-2);

  &:hover,
  &:focus {
    background-color: var(--primary-color-hover);
    border-color: var(--primary-color-hover);
  }
}

.deleteMenuItem {
  color: var(--error-color) !important;

  &:hover {
    background-color: var(--bg-hover) !important;
  }
}

.deleteModal {
  :global(.ant-modal-content) {
    border-radius: 8px;
    overflow: hidden;
    background-color: var(--bg-modal);
  }

  :global(.ant-modal-header) {
    border-bottom: none;
    padding: 24px 24px 0;
    background-color: var(--bg-modal);
  }

  :global(.ant-modal-body) {
    padding: 20px 24px 24px;
    background-color: var(--bg-modal);
  }
}

.deleteModalTitle {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--text-color);
  font-weight: 500;

  .deleteIcon {
    color: var(--error-color);
    font-size: 22px;
  }
}

.deleteModalContent {
  p {
    margin: 0;
    font-size: 14px;
    color: var(--text-color);

    &.deleteWarning {
      color: var(--text-color-tertiary);
      margin-top: 8px;
      font-size: 13px;
    }
  }
}

.deleteModalActions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  margin-top: 24px;
}

.cancelButton {
  &:hover {
    border-color: var(--border-color-base);
    color: var(--text-color);
  }
}

.confirmButton {
  background-color: var(--error-color);
  border-color: var(--error-color);

  &:hover,
  &:focus {
    background-color: var(--error-color);
    border-color: var(--error-color);
    opacity: 0.8;
  }

  &:active {
    background-color: var(--error-color);
    border-color: var(--error-color);
    opacity: 0.9;
  }

  &[disabled] {
    background-color: var(--error-color);
    border-color: var(--error-color);
    opacity: 0.7;
  }
}

.messageDetails {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 8px;
  flex-wrap: wrap;

  .itemRecipient {
    color: var(--text-color-secondary);
    font-size: 14px;
    background: var(--bg-hover);
    padding: 4px 8px;
    border-radius: 4px;
    display: flex;
    align-items: center;
  }

  .itemPrice {
    display: flex;
    align-items: center;
    gap: 4px;
    background: var(--primary-color-light);
    padding: 4px 8px;
    border-radius: 4px;
    border: 1px solid var(--primary-color-fade);

    .priceLabel {
      color: var(--text-color-tertiary);
      font-size: 13px;
    }

    .priceValue {
      color: var(--info-color);
      font-weight: 500;
      font-size: 14px;
    }
  }
}

// Responsive styles
@media (max-width: 767px) {
  .contentContainer {
    flex-direction: column;
    margin-left: 0px;
  }

  .calendarSection {
    border-right: none;
  }

  .sidebarSection {
    width: 100%;
    border-left: none;
    border-top: 1px solid var(--border-color-split);
  }

  .calendar {
    :global(.ant-picker-calendar-date) {
      min-height: 60px;
    }
  }

  .dateCell {
    min-height: 60px;
  }

  .header {
    padding: 16px 8px;
  }

  .headerRight {
    padding-right: 8px;
  }

  .scheduledItems {
    flex: 1;
  }

  .itemMedia {
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    gap: 4px;
  }

  .editContentWrapper {
    margin: 0 -8px;
  }

  .editActions {
    padding: 0 8px;
  }

  .deleteModal {
    max-width: 90%;
    margin: auto;

    :global(.ant-modal-content) {
      border-radius: 0;
    }
  }

  .deleteModalActions {
    margin-top: 32px;

    button {
      flex: 1;
      padding: 6px 0;
    }
  }

  .statsContainer {
    gap: 12px;
    margin: 16px 0;
    padding: 0 8px;
  }

  .statItem {
    flex: 1;
    justify-content: center;
    padding: 12px 8px;

    .statCount {
      font-size: 16px;
    }

    .statLabel {
      font-size: 13px;
    }
  }

  .sidebarMenuButton {
    display: none;
  }

  .messageDetails {
    gap: 8px;
    margin-bottom: 6px;

    .itemRecipient,
    .itemPrice {
      font-size: 13px;
      padding: 3px 6px;
    }
  }
}

:global(.ant-modal) {
  .ant-modal-body {
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background-color: var(--border-color-base);
      border-radius: 3px;
    }

    &::-webkit-scrollbar-track {
      background-color: transparent;
    }
  }
}
