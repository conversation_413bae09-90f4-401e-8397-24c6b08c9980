// Theme variables for both light and dark modes
@import './vars.less';

:root {
  // Background colors
  --bg-white: #fff;
  --bg-primary: #7e57c2;
  --bg-secondary: #f5f5f5;
  --bg-panel-white: #fff;
  --bg-card: #fff;
  --bg-hover: rgba(0, 0, 0, 0.05);
  --bg-active: rgba(0, 0, 0, 0.1);
  --bg-disabled: #f5f5f5;
  --bg-overlay: rgba(0, 0, 0, 0.65);
  --bg-modal: #fff;
  --bg-drawer: #fff;

  // Shadow system
  --shadow-1: 0 2px 8px rgba(0, 0, 0, 0.15);
  --shadow-2: 0 4px 16px rgba(0, 0, 0, 0.2);
  --shadow-3: 0 8px 24px rgba(0, 0, 0, 0.25);
  --shadow-1-down: 0 4px 12px rgba(0, 0, 0, 0.15);

  // Status colors
  --primary-color: #7e57c2;
  --primary-color-fade: rgba(126, 87, 194, 0.3);
  --primary-color-light: rgba(126, 87, 194, 0.15);
  --primary-color-hover: #9575cd;
  --success-color: #4caf50;
  --error-color: #f44336;
  --warning-color: #ffc107;
  --info-color: #7e57c2;

  // Theme colors
  --theme-color: #9575cd;
  --gradient-primary: linear-gradient(135deg, #7e57c2, #9575cd);
  --primary-gradient: linear-gradient(0deg, #9575cd, #7e57c2);
  --primary-gradient-hover: linear-gradient(0deg, #b39ddb, #9575cd);
  --gradient-secondary: linear-gradient(135deg, #f5f5f5, #e8e8e8);

  // Text colors
  --color-white: #fff;
  --color-black: #000;
  --text-color: @text-color;
  --text-color-secondary: @text-color-secondary;
  --text-color-tertiary: rgba(0, 0, 0, 0.45);
  --text-color-disabled: rgba(0, 0, 0, 0.25);
  --text-color-inverse: #fff;
  --text-color-light: rgba(0, 0, 0, 0.65);
  --text-color-muted: rgba(0, 0, 0, 0.45);

  // Border colors
  --border-color-base: @border-color-base;
  --border-color-split: @border-color-split;
  --border-color-light: #e8e8e8;
  --light-grey-border: #f0f0f0;
  --border-color-dark: #d9d9d9;

  // Component colors
  --component-background: #fff;
  --item-hover-bg: rgba(0, 0, 0, 0.05);
  --item-active-bg: rgba(0, 0, 0, 0.1);
  --card-background: #fff;
  --input-background: #fff;
  --button-background: #fff;
  --button-secondary-bg: #fff;
  --button-secondary-text: @text-color;
  --button-secondary-hover-bg: #f5f5f5;
  --button-secondary-hover-text: @primary-color;

  // Shadow colors
  --shadow-color: rgba(0, 0, 0, 0.15);
  --shadow-color-light: rgba(0, 0, 0, 0.1);
  --shadow-color-dark: rgba(0, 0, 0, 0.2);

  // Theme mode indicator
  --theme-mode: 'light';
}

// Theme transition
.theme-transition {
  * {
    transition: background-color 0.3s ease, color 0.3s ease,
      border-color 0.3s ease, box-shadow 0.3s ease !important;
  }
}

.dark-mode {
  // Background colors
  --bg-white: #121212;
  --bg-primary: #8b6dd1;
  --bg-secondary: #1e1e1e;
  --bg-panel-white: #1e1e1e;
  --bg-card: #242424;
  --bg-hover: rgba(255, 255, 255, 0.15);
  --bg-active: rgba(255, 255, 255, 0.22);
  --bg-disabled: #2a2a2a;
  --bg-overlay: rgba(0, 0, 0, 0.85);
  --bg-modal: #242424;
  --bg-drawer: #1e1e1e;

  // Text colors
  --color-white: #fff;
  --color-black: #000;
  --text-color: rgba(255, 255, 255, 0.95);
  --text-color-secondary: rgba(255, 255, 255, 0.78);
  --text-color-tertiary: rgba(255, 255, 255, 0.58);
  --text-color-disabled: rgba(255, 255, 255, 0.45);
  --text-color-inverse: #000;
  --text-color-light: rgba(255, 255, 255, 0.68);
  --text-color-muted: rgba(255, 255, 255, 0.48);

  // Border colors
  --border-color-base: #404040;
  --border-color-split: #333333;
  --border-color-light: #404040;
  --light-grey-border: #404040;
  --border-color-dark: #505050;

  // Component colors
  --component-background: #242424;
  --item-hover-bg: rgba(255, 255, 255, 0.15);
  --item-active-bg: rgba(255, 255, 255, 0.22);
  --card-background: #242424;
  --input-background: #2a2a2a;
  --button-background: #2a2a2a;
  --button-secondary-bg: #3a3a3a;
  --button-secondary-text: rgba(255, 255, 255, 0.95);
  --button-secondary-hover-bg: #4a4a4a;
  --button-secondary-hover-text: #8b6dd1;

  // Shadow colors
  --shadow-color: rgba(0, 0, 0, 0.6);
  --shadow-color-light: rgba(0, 0, 0, 0.4);
  --shadow-color-dark: rgba(0, 0, 0, 0.8);
  --shadow-1: 0 2px 8px rgba(0, 0, 0, 0.3);
  --shadow-2: 0 4px 16px rgba(0, 0, 0, 0.4);
  --shadow-3: 0 8px 24px rgba(0, 0, 0, 0.5);
  --shadow-1-down: 0 4px 12px rgba(0, 0, 0, 0.4);

  // Status colors
  --primary-color: #8b6dd1;
  --primary-color-fade: rgba(139, 109, 209, 0.3);
  --primary-color-light: rgba(139, 109, 209, 0.15);
  --primary-color-hover: #9b7de1;
  --success-color: #10d13a;
  --error-color: #ff5252;
  --warning-color: #ffc107;
  --info-color: #8b6dd1;

  // Theme colors
  --theme-color: #a47dd9;
  --gradient-primary: linear-gradient(135deg, #8b6dd1, #a47dd9);
  --primary-gradient: linear-gradient(0deg, #a47dd9, #8b6dd1);
  --primary-gradient-hover: linear-gradient(0deg, #b48de9, #9b7de1);
  --gradient-secondary: linear-gradient(135deg, #1a1a1a, #2a2a2a);

  // Message colors
  --bg-message-mine: rgba(164, 125, 217, 0.15);
  --bg-message-other: rgba(255, 255, 255, 0.08);
  --text-message-mine: rgba(255, 255, 255, 0.87);
  --text-message-other: rgba(255, 255, 255, 0.87);

  // Image filters
  --image-filter: brightness(0.9) contrast(1.1);

  // Theme mode indicator
  --theme-mode: 'dark';
  --shadow-1: 0 2px 8px rgba(0, 0, 0, 0.3);
}
