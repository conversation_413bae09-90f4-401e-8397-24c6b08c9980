const axios = require('axios');

const API_BASE_URL = 'http://localhost:3000';

async function testSharedProgressFlow() {
  console.log('🎬 Testing shared progress flow (0-40% upload, 40-100% conversion)...\n');
  
  const progressUpdates = [];
  
  try {
    // Mock progress callback to track progress updates
    const mockProgressCallback = (progress) => {
      progressUpdates.push({
        timestamp: new Date().toISOString(),
        percent: progress.percent,
        phase: progress.percent <= 40 ? 'UPLOAD' : 'CONVERSION',
        currentTime: progress.currentTime,
        targetTime: progress.targetTime,
        targetSize: progress.targetSize,
        currentFps: progress.currentFps,
        currentKbps: progress.currentKbps
      });
      
      console.log(`📊 Progress: ${progress.percent}% [${progress.percent <= 40 ? 'UPLOAD' : 'CONVERSION'}] - ${progress.targetSize}`);
    };

    // Test the video converter client service
    // Note: This would normally be called from within the NestJS application
    console.log('🚀 This test demonstrates the expected progress flow:');
    console.log('   0-40%:  Upload phase');
    console.log('   40-100%: Conversion phase');
    console.log('');
    
    // Simulate upload progress (0-40%)
    console.log('📤 Simulating upload phase (0-40%)...');
    for (let i = 0; i <= 40; i += 5) {
      mockProgressCallback({
        percent: i,
        currentTime: '00:00:00',
        targetTime: '00:00:00',
        targetSize: `${Math.round((i / 40) * 1024)}kB`,
        currentFps: 0,
        currentKbps: 0
      });
      await new Promise(resolve => setTimeout(resolve, 100)); // Simulate time
    }
    
    console.log('✅ Upload phase completed (40%)\n');
    
    // Simulate conversion progress (40-100%)
    console.log('🔄 Simulating conversion phase (40-100%)...');
    for (let i = 0; i <= 100; i += 10) {
      // Scale conversion progress to 40-100% range
      const scaledPercent = 40 + (i * 0.6);
      mockProgressCallback({
        percent: Math.round(scaledPercent),
        currentTime: `00:00:${String(Math.round(i * 0.3)).padStart(2, '0')}`,
        targetTime: '00:00:30',
        targetSize: i === 100 ? 'Complete' : `${Math.round(i * 10)}kB`,
        currentFps: i < 100 ? 30 : 0,
        currentKbps: i < 100 ? 1000 : 0
      });
      await new Promise(resolve => setTimeout(resolve, 200)); // Simulate time
    }
    
    console.log('✅ Conversion phase completed (100%)\n');
    
    // Analyze progress flow
    console.log('📋 Progress Flow Analysis:');
    console.log(`Total progress updates: ${progressUpdates.length}`);
    
    const uploadUpdates = progressUpdates.filter(p => p.phase === 'UPLOAD');
    const conversionUpdates = progressUpdates.filter(p => p.phase === 'CONVERSION');
    
    console.log(`Upload phase updates: ${uploadUpdates.length} (0-40%)`);
    console.log(`Conversion phase updates: ${conversionUpdates.length} (40-100%)`);
    
    if (uploadUpdates.length > 0) {
      console.log(`Upload range: ${uploadUpdates[0].percent}% - ${uploadUpdates[uploadUpdates.length - 1].percent}%`);
    }
    
    if (conversionUpdates.length > 0) {
      console.log(`Conversion range: ${conversionUpdates[0].percent}% - ${conversionUpdates[conversionUpdates.length - 1].percent}%`);
    }
    
    // Verify progress flow integrity
    const isValidFlow = progressUpdates.every((update, index) => {
      if (index === 0) return true;
      return update.percent >= progressUpdates[index - 1].percent;
    });
    
    console.log(`\n✅ Progress flow integrity: ${isValidFlow ? 'VALID' : 'INVALID'}`);
    console.log(`Final progress: ${progressUpdates[progressUpdates.length - 1]?.percent || 0}%`);
    
    return {
      success: true,
      totalUpdates: progressUpdates.length,
      uploadUpdates: uploadUpdates.length,
      conversionUpdates: conversionUpdates.length,
      validFlow: isValidFlow,
      finalProgress: progressUpdates[progressUpdates.length - 1]?.percent || 0
    };
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    return {
      success: false,
      error: error.message,
      progressUpdates: progressUpdates.length
    };
  }
}

async function runSharedProgressTest() {
  console.log('🧪 Starting Shared Progress Flow Test\n');
  
  try {
    const result = await testSharedProgressFlow();
    
    console.log('\n📋 Test Results:');
    console.log(`Success: ${result.success ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`Total Updates: ${result.totalUpdates || 0}`);
    console.log(`Upload Updates: ${result.uploadUpdates || 0}`);
    console.log(`Conversion Updates: ${result.conversionUpdates || 0}`);
    console.log(`Valid Flow: ${result.validFlow ? '✅ YES' : '❌ NO'}`);
    console.log(`Final Progress: ${result.finalProgress || 0}%`);
    
    if (result.error) {
      console.log(`Error: ${result.error}`);
    }
    
    console.log('\n🎯 Expected Implementation:');
    console.log('1. Upload progress: 0-40% (file upload to video-converter-api)');
    console.log('2. Conversion progress: 40-100% (actual video processing)');
    console.log('3. Progress scaling: Conversion 0-100% → Overall 40-100%');
    console.log('4. Socket communication: Real-time updates throughout both phases');
    
  } catch (error) {
    console.error('❌ Test execution failed:', error.message);
  }
}

// Run the test
runSharedProgressTest();
