import { Injectable } from '@nestjs/common';
import { Model } from 'mongoose';
import { ObjectId } from 'mongodb';
import { ConfigService } from 'nestjs-config';
import {
  StringHelper,
  QueueEventService,
  QueueEvent,
  getConfig,
  EntityNotFoundException
} from 'src/kernel';
import { writeFileSync, unlinkSync, existsSync, createReadStream } from 'fs';
import { join } from 'path';
import * as jwt from 'jsonwebtoken';
import { toPosixPath } from 'src/kernel/helpers/string.helper';
import { InjectModel } from '@nestjs/mongoose';
import { plainToInstance } from 'class-transformer';
import { SocketUserService } from 'src/modules/socket/services/socket-user.service';
import { IMulterUploadedFile } from '../lib/multer/multer.utils';
import { FileDto } from '../dtos';
import { IFileUploadOptions } from '../lib';
import { ImageService } from './image.service';
import { FileVideoService, IConvertProgress } from './video.service';
import { VideoConverterClientService } from './video-converter-client.service';
import { File } from '../schemas';

const VIDEO_QUEUE_CHANNEL = 'VIDEO_PROCESS';
const PHOTO_QUEUE_CHANNEL = 'PHOTO_PROCESS';

export const FILE_EVENT = {
  VIDEO_PROCESSED: 'VIDEO_PROCESSED',
  PHOTO_PROCESSED: 'PHOTO_PROCESSED',
  FILE_STATUS_UPDATED: 'FILE_STATUS_UPDATED',
  FILE_PROGRESS_UPDATED: 'FILE_PROGRESS_UPDATED'
};

@Injectable()
export class FileService {
  constructor(
    @InjectModel(File.name) private readonly FileModel: Model<File>,
    private readonly config: ConfigService,
    private readonly imageService: ImageService,
    private readonly videoService: FileVideoService,
    private readonly videoConverterClientService: VideoConverterClientService,
    private readonly queueEventService: QueueEventService,
    private readonly socketUserService: SocketUserService
  ) {
    // Create multiple video processing instances (up to 10) for concurrent processing
    this.setupVideoProcessingWorkers();

    // Create multiple photo processing instances (up to 10) for concurrent processing
    this.setupPhotoProcessingWorkers();
  }

  /**
   * Setup multiple video processing workers for concurrent processing
   * Maximum 10 workers can process videos simultaneously
   */
  private async setupVideoProcessingWorkers() {
    const maxWorkers = 10;
    console.log(`Setting up ${maxWorkers} video processing workers...`);

    for (let i = 0; i < maxWorkers; i++) {
      await this.queueEventService.subscribe(
        VIDEO_QUEUE_CHANNEL,
        'PROCESS_VIDEO',
        this._processVideo.bind(this)
      );
    }

    console.log(
      `Video processing workers setup complete! ${maxWorkers} workers ready.`
    );
  }

  /**
   * Setup multiple photo processing workers for concurrent processing
   * Maximum 10 workers can process photos simultaneously
   */
  private async setupPhotoProcessingWorkers() {
    const maxWorkers = 10;
    console.log(`Setting up ${maxWorkers} photo processing workers...`);

    for (let i = 0; i < maxWorkers; i++) {
      await this.queueEventService.subscribe(
        PHOTO_QUEUE_CHANNEL,
        'PROCESS_PHOTO',
        this._processPhoto.bind(this)
      );
    }

    console.log(
      `Photo processing workers setup complete! ${maxWorkers} workers ready.`
    );
  }

  public async findById(id: string | ObjectId | any): Promise<FileDto> {
    const model = await this.FileModel.findById(id);
    if (!model) return null;
    return plainToInstance(FileDto, model.toObject());
  }

  public async findByIds(
    ids: string[] | ObjectId[] | any[]
  ): Promise<FileDto[]> {
    const items = await this.FileModel.find({
      _id: {
        $in: ids
      }
    });

    return items.map((i) => plainToInstance(FileDto, i.toObject()));
  }

  public async countByRefType(itemType: string): Promise<any> {
    const count = await this.FileModel.countDocuments({
      refItems: { $elemMatch: { itemType } }
    });
    return count;
  }

  public async findByRefType(
    itemType: string,
    limit: number,
    offset: number
  ): Promise<any> {
    const items = await this.FileModel.find({
      refItems: { $elemMatch: { itemType } }
    })
      .limit(limit)
      .skip(offset * limit);
    return items.map((item) => plainToInstance(FileDto, item.toObject()));
  }

  public async createFromMulter(
    type: string,
    multerData: IMulterUploadedFile,
    options?: IFileUploadOptions
  ): Promise<FileDto> {
    // eslint-disable-next-line no-param-reassign
    options = options || {};
    const publicDir = this.config.get('file.publicDir');
    const photoDir = this.config.get('file.photoDir');
    const thumbnails = [];
    let blurImagePath = '';
    // replace new photo without exif, ignore video
    if (multerData.mimetype.includes('image')) {
      const buffer = await this.imageService.replaceWithoutExif(
        multerData.path
      );
      let thumbBuffer: any = null;
      let blurBuffer: any = null;
      if (options.generateThumbnail) {
        thumbBuffer = (await this.imageService.createThumbnail(
          multerData.path,
          options?.thumbnailSize || { width: 280, height: 220 }
        )) as Buffer;
        const thumbName = `${StringHelper.randomString(5)}_thumb${StringHelper.getExt(multerData.path)}`;
        !options?.replaceByThumbnail &&
          writeFileSync(join(photoDir, thumbName), thumbBuffer);
        !options?.replaceByThumbnail &&
          thumbnails.push({
            thumbnailSize: options.thumbnailSize,
            path: toPosixPath(join(photoDir, thumbName).replace(publicDir, '')),
            absolutePath: join(photoDir, thumbName)
          });

        // generate blur image
        blurBuffer = (await this.imageService.blur(multerData.path)) as Buffer;
      }
      unlinkSync(multerData.path);
      writeFileSync(
        multerData.path,
        options?.replaceByThumbnail && thumbBuffer ? thumbBuffer : buffer
      );
      if (blurBuffer) {
        blurImagePath = toPosixPath(
          join(
            photoDir,
            `${StringHelper.randomString(5)}_blur_${StringHelper.getExt(multerData.path)}`
          )
        );
        writeFileSync(blurImagePath, blurBuffer);
      }
    }

    const data = {
      type,
      name: multerData.filename,
      description: '', // TODO - get from options
      mimeType: multerData.mimetype,
      server: options.server || 'local',
      // todo - get path from public
      path: multerData.path.replace(publicDir, ''),
      absolutePath: multerData.path,
      blurImagePath: blurImagePath.replace(publicDir, ''),
      // TODO - update file size
      size: multerData.size,
      createdAt: new Date(),
      updatedAt: new Date(),
      createdBy: options.uploader ? options.uploader._id : null,
      updatedBy: options.uploader ? options.uploader._id : null
    };

    const file = await this.FileModel.create(data);

    // Process video conversion if requested
    if (options.convertMp4 && multerData.mimetype.includes('video')) {
      // Queue video processing for conversion via video-converter-api
      await this.queueProcessVideo(file._id, {
        publishChannel: 'FILE_UPLOAD_CHANNEL',
        meta: {
          convertMp4: true,
          originalOptions: options
        }
      });
    }

    return plainToInstance(FileDto, file.toObject());
  }

  public async addRef(
    fileId: string | ObjectId,
    ref: {
      itemId: ObjectId;
      itemType: string;
    }
  ) {
    return this.FileModel.updateOne(
      { _id: fileId },
      {
        $addToSet: {
          refItems: ref
        }
      }
    );
  }

  private async removePhysicalFile(file) {
    const filePaths = [
      {
        absolutePath: file.absolutePath,
        path: file.path
      },
      {
        absolutePath: file.blurImagePath,
        path: file.blurImagePath
      }
    ].concat(file.thumbnails || []);

    filePaths.forEach((fp) => {
      if (existsSync(fp.absolutePath)) {
        unlinkSync(fp.absolutePath);
      } else {
        const publicDir = this.config.get('file.publicDir');
        if (existsSync(fp.path)) {
          const filePublic = join(publicDir, fp.path);
          existsSync(filePublic) && unlinkSync(filePublic);
        }
      }
    });
  }

  public async remove(fileId: string | ObjectId | any) {
    const file = await this.FileModel.findOne({ _id: fileId });
    if (!file) {
      return false;
    }

    await file.deleteOne();
    await this.removePhysicalFile(file);
    // TODO - fire event
    return true;
  }

  public async removeIfNotHaveRef(fileId: string | ObjectId) {
    const file = await this.FileModel.findOne({ _id: fileId });
    if (!file) {
      return false;
    }

    if (file.refItems && !file.refItems.length) {
      return false;
    }

    await file.deleteOne();

    await this.removePhysicalFile(file);

    // TODO - fire event
    return true;
  }

  // TODO - fix here, currently we just support local server, need a better solution if scale?
  /**
   * generate mp4 video & thumbnail
   * @param fileId
   * @param options
   */
  public async queueProcessVideo(
    fileId: string | ObjectId | any,
    options?: {
      meta: Record<string, any>;
      publishChannel: string;
    }
  ) {
    // add queue and convert file to mp4 and generate thumbnail
    const file = await this.FileModel.findOne({ _id: fileId });
    if (!file || file.status === 'processing') {
      return false;
    }
    await this.queueEventService.publish(
      new QueueEvent({
        channel: VIDEO_QUEUE_CHANNEL,
        eventName: 'processVideo',
        data: {
          file: plainToInstance(FileDto, file.toObject()),
          options
        }
      })
    );
    return true;
  }

  private async _processVideo(event: QueueEvent) {
    if (event.eventName !== 'processVideo') {
      return;
    }
    const fileData = event.data.file as FileDto;
    const options = event.data.options || {};
    try {
      await this.FileModel.updateOne(
        { _id: fileData._id },
        {
          $set: {
            status: 'processing'
          }
        }
      );

      // Emit initial processing status via WebSocket
      if (fileData.createdBy) {
        await this.socketUserService.emitToUsers(
          fileData.createdBy,
          'file_status_updated',
          {
            fileId: fileData._id,
            status: 'processing',
            eventType: FILE_EVENT.FILE_STATUS_UPDATED
          }
        );
      }

      // get thumb of the file, then convert to mp4
      const publicDir = this.config.get('file.publicDir');
      const videoDir = this.config.get('file.videoDir');
      // eslint-disable-next-line no-nested-ternary
      const videoPath = existsSync(fileData.absolutePath)
        ? fileData.absolutePath
        : existsSync(join(publicDir, fileData.path))
          ? join(publicDir, fileData.path)
          : null;

      if (!videoPath) {
        // eslint-disable-next-line no-throw-literal
        throw 'No file file!';
      }

      // convert to mp4 if it is not support html5 or if convertMp4 is explicitly requested
      const isSupportHtml5 =
        await this.videoConverterClientService.isSupportHtml5(videoPath);
      const shouldConvert = !isSupportHtml5 || options.meta?.convertMp4;
      let deleteOriginalFile = false;
      let respVideo = {
        toPath: videoPath
      };

      if (shouldConvert) {
        deleteOriginalFile = true;

        // Set up progress callback to emit websocket events
        const onProgress = async (progress: IConvertProgress) => {
          if (fileData.createdBy) {
            await this.socketUserService.emitToUsers(
              fileData.createdBy,
              'file_progress_updated',
              {
                fileId: fileData._id,
                status: 'processing',
                progress: {
                  percent: progress.percent,
                  currentTime: progress.currentTime,
                  targetTime: progress.targetTime,
                  currentFps: progress.currentFps,
                  currentKbps: progress.currentKbps,
                  targetSize: progress.targetSize
                },
                eventType: FILE_EVENT.FILE_PROGRESS_UPDATED
              }
            );
          }
        };

        // Use video converter API service instead of local ffmpeg
        respVideo = await this.videoConverterClientService.convert2Mp4(
          videoPath,
          {
            onProgress,
            size: options.meta?.originalOptions?.size
          }
        );
      }

      // Emit 90% progress when conversion completes and thumbnail creation starts
      if (fileData.createdBy) {
        await this.socketUserService.emitToUsers(
          fileData.createdBy,
          'file_progress_updated',
          {
            fileId: fileData._id,
            status: 'processing',
            progress: {
              percent: 90,
              currentTime: '00:00:00',
              targetTime: '00:00:00',
              currentFps: 0,
              currentKbps: 0,
              targetSize: 'Starting thumbnail creation...'
            },
            eventType: FILE_EVENT.FILE_PROGRESS_UPDATED
          }
        );
      }

      // delete old video and replace with new one
      const newAbsolutePath = respVideo.toPath;
      const newPath = respVideo.toPath.replace(publicDir, '');
      const meta = await this.videoService.getMetaData(videoPath);

      console.log('Get Video Meta Data');

      const videoMeta = meta.streams.find((s: any) => s.codec_type === 'video');
      const { width, height } = videoMeta || {};

      // Create thumbnails with progress tracking (90-100%)
      const respThumb = await this.videoService.createThumbs(videoPath, {
        toFolder: videoDir,
        size: options?.size || '350x?', // thumnail dimension
        count: options?.count || 3, // number of thumbnails-screenshots
        onProgress: async (progress: IConvertProgress) => {
          if (fileData.createdBy) {
            // Scale thumbnail progress from 0-100% to 90-100% (10% range)
            const scaledPercent = 90 + (progress.percent * 0.1);
            const finalPercent = Math.min(Math.round(scaledPercent), 100);

            await this.socketUserService.emitToUsers(
              fileData.createdBy,
              'file_progress_updated',
              {
                fileId: fileData._id,
                status: 'processing',
                progress: {
                  percent: finalPercent,
                  currentTime: progress.currentTime,
                  targetTime: progress.targetTime,
                  currentFps: progress.currentFps,
                  currentKbps: progress.currentKbps,
                  targetSize: progress.targetSize
                },
                eventType: FILE_EVENT.FILE_PROGRESS_UPDATED
              }
            );
          }
        }
      });
      const thumbnails = respThumb.map((name) => ({
        absolutePath: join(videoDir, name),
        path: join(videoDir, name).replace(publicDir, '')
      }));

      console.log('Create Thumbs files');

      // generate blur image
      let blurImagePath;
      if (thumbnails.length) {
        const blurBuffer = await this.imageService.blur(
          thumbnails[0].absolutePath
        );
        const photoDir = this.config.get('file.photoDir');
        blurImagePath = join(
          photoDir,
          `${StringHelper.randomString(5)}_blur_${StringHelper.getExt(thumbnails[0].absolutePath)}`
        );
        writeFileSync(blurImagePath, blurBuffer);
      }

      console.log('Create Blur Image');

      if (deleteOriginalFile && existsSync(videoPath)) unlinkSync(videoPath);
      const updated = await this.FileModel.updateOne(
        { _id: fileData._id },
        {
          $set: {
            status: 'finished',
            absolutePath: newAbsolutePath,
            path: toPosixPath(newPath),
            thumbnails,
            blurImagePath: toPosixPath(blurImagePath.replace(publicDir, '')),
            duration: parseInt(meta.format.duration, 10),
            width,
            height
          }
        }
      );
    } catch (e) {
      const error = await e;
      console.log(error);
      // await this.FileModel.updateOne(
      //   { _id: fileData._id },
      //   {
      //     $set: {
      //       status: 'error',
      //       error: error?.stack || e
      //     }
      //   }
      // );
    } finally {
      // TODO - fire event to subscriber
      if (options.publishChannel) {
        await this.queueEventService.publish(
          new QueueEvent({
            channel: options.publishChannel,
            eventName: FILE_EVENT.VIDEO_PROCESSED,
            data: {
              meta: options.meta,
              fileId: fileData._id
            }
          })
        );
      }

      await this.FileModel.updateOne(
        { _id: fileData._id },
        {
          $set: {
            status: 'finished'
          }
        }
      );

      // Emit file status update via WebSocket
      if (fileData.createdBy) {
        await this.socketUserService.emitToUsers(
          fileData.createdBy,
          'file_status_updated',
          {
            fileId: fileData._id,
            status: 'finished',
            eventType: FILE_EVENT.FILE_STATUS_UPDATED
          }
        );
      }
    }
  }

  /**
   * process to create photo thumbnails
   * @param fileId file item
   * @param options
   */
  public async queueProcessPhoto(
    fileId: string | ObjectId,
    options?: {
      meta?: Record<string, any>;
      publishChannel?: string;
      thumbnailSize?: {
        width: number;
        height?: number;
        blur?: number | boolean;
      };
    }
  ) {
    // add queue and convert file to mp4 and generate thumbnail
    const file = await this.FileModel.findOne({ _id: fileId });
    if (!file || file.status === 'processing') {
      return false;
    }
    await this.queueEventService.publish(
      new QueueEvent({
        channel: PHOTO_QUEUE_CHANNEL,
        eventName: 'processPhoto',
        data: {
          file: plainToInstance(FileDto, file.toObject()),
          options
        }
      })
    );
    return true;
  }

  private async _processPhoto(event: QueueEvent) {
    if (event.eventName !== 'processPhoto') {
      return;
    }
    const fileData = event.data.file as FileDto;
    const options = event.data.options || {};
    try {
      await this.FileModel.updateOne(
        { _id: fileData._id },
        {
          $set: {
            status: 'processing'
          }
        }
      );

      // Emit initial processing status via WebSocket
      if (fileData.createdBy) {
        await this.socketUserService.emitToUsers(
          fileData.createdBy,
          'file_status_updated',
          {
            fileId: fileData._id,
            status: 'processing',
            eventType: FILE_EVENT.FILE_STATUS_UPDATED
          }
        );
      }

      // get thumb of the file, then convert to mp4
      const publicDir = this.config.get('file.publicDir');
      const photoDir = this.config.get('file.photoDir');
      // eslint-disable-next-line no-nested-ternary
      const photoPath = existsSync(fileData.absolutePath)
        ? fileData.absolutePath
        : existsSync(join(publicDir, fileData.path))
          ? join(publicDir, fileData.path)
          : null;

      if (!photoPath) {
        // eslint-disable-next-line no-throw-literal
        throw 'No file!';
      }

      const meta = await this.imageService.getMetaData(photoPath);
      const buffer = (await this.imageService.createThumbnail(
        photoPath,
        options.thumbnailSize || {
          width: 250,
          height: null,
          blur: false
        }
      )) as Buffer;

      // store to a file
      const thumbName = `${StringHelper.randomString(5)}_thumb${StringHelper.getExt(photoPath)}`;
      writeFileSync(join(photoDir, thumbName), buffer);

      // create blury image
      const blurBuffer = await this.imageService.blur(buffer as Buffer);
      const blurImagePath = join(
        photoDir,
        `${StringHelper.randomString(5)}_blur_${StringHelper.getExt(thumbName)}`
      );
      writeFileSync(blurImagePath, blurBuffer);

      await this.FileModel.updateOne(
        { _id: fileData._id },
        {
          $set: {
            status: 'finished',
            width: meta.width,
            height: meta.height,
            thumbnails: [
              {
                path: toPosixPath(
                  join(photoDir, thumbName).replace(publicDir, '') as string
                ),
                absolutePath: join(photoDir, thumbName)
              }
            ],
            blurImagePath: toPosixPath(blurImagePath.replace(publicDir, ''))
          }
        }
      );
    } catch (e) {
      const error = await e;
      await this.FileModel.updateOne(
        { _id: fileData._id },
        {
          $set: {
            status: 'error',
            error: error?.stack || error
          }
        }
      );

      // Emit error status via WebSocket
      if (fileData.createdBy) {
        await this.socketUserService.emitToUsers(
          fileData.createdBy,
          'file_status_updated',
          {
            fileId: fileData._id,
            status: 'error',
            error: error?.message || 'Photo processing failed',
            eventType: FILE_EVENT.FILE_STATUS_UPDATED
          }
        );
      }
    } finally {
      // fire event to subscriber
      if (options.publishChannel) {
        await this.queueEventService.publish(
          new QueueEvent({
            channel: options.publishChannel,
            eventName: FILE_EVENT.PHOTO_PROCESSED,
            data: {
              meta: options.meta,
              fileId: fileData._id
            }
          })
        );
      }

      // Emit file status update via WebSocket
      if (fileData.createdBy) {
        await this.socketUserService.emitToUsers(
          fileData.createdBy,
          'file_status_updated',
          {
            fileId: fileData._id,
            status: 'finished',
            eventType: FILE_EVENT.FILE_STATUS_UPDATED
          }
        );
      }
    }
  }

  /**
   * just generate key for
   */
  private generateJwt(fileId: string | ObjectId) {
    // 3h
    const expiresIn = 60 * 60 * 3;
    return jwt.sign(
      {
        fileId
      },
      process.env.TOKEN_SECRET,
      {
        expiresIn
      }
    );
  }

  /**
   * generate download file url with expired time check
   * @param fileId
   * @param param1
   */
  public async generateDownloadLink(fileId: string | ObjectId) {
    const newUrl = new URL('files/download', getConfig('app').baseUrl);
    newUrl.searchParams.append('key', this.generateJwt(fileId));
    return newUrl.href;
  }

  public async getStreamToDownload(key: string) {
    try {
      const decoded = jwt.verify(key, process.env.TOKEN_SECRET);
      const file = await this.FileModel.findById(decoded.fileId);
      if (!file) throw new EntityNotFoundException();
      let filePath;
      const publicDir = this.config.get('file.publicDir');
      if (existsSync(file.absolutePath)) {
        filePath = file.absolutePath;
      } else if (existsSync(join(publicDir, file.path))) {
        filePath = join(publicDir, file.path);
      } else {
        throw new EntityNotFoundException();
      }

      return {
        file,
        stream: createReadStream(filePath)
      };
    } catch (e) {
      throw new EntityNotFoundException();
    }
  }
}
