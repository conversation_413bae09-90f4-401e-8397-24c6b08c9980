import { spawn } from 'child_process';
import * as ffmpeg from 'fluent-ffmpeg';
import { join } from 'path';
import { StringHelper } from 'src/kernel';
import { getExt } from 'src/kernel/helpers/string.helper';
import { ConvertMp4ErrorException } from '../exceptions';

export interface IConvertOptions {
  toPath?: string;
  size?: string; // https://github.com/fluent-ffmpeg/node-fluent-ffmpeg#video-frame-size-options
  onProgress?: (progress: IConvertProgress) => void;
}

export interface IConvertProgress {
  percent: number;
  currentTime: string;
  targetSize: string;
  currentFps: number;
  currentKbps: number;
  targetTime: string;
}

export interface IConvertResponse {
  fileName: string;
  toPath: string;
}

export class FileVideoService {
  public async convert2Mp4(
    filePath: string,
    options = {} as IConvertOptions
  ): Promise<IConvertResponse> {
    try {
      const fileName = `${StringHelper.randomString(5)}_${StringHelper.getFileName(filePath, true)}.mp4`;
      const toPath =
        options.toPath || join(StringHelper.getFilePath(filePath), fileName);

      // TODO - h264 just supports max 4k video

      return new Promise((resolve, reject) => {
        // have error, we have to build manually command line
        // eslint-disable-next-line new-cap
        // const command = new ffmpeg(filePath)
        //   // set target codec
        //   .videoCodec('libx264')
        //   // .addOption('-vf', 'scale=2*trunc(iw/2):-2')
        //   // QuickTime compatibility, Note: Requires dimensions to be divisible by 2.
        //   .outputOptions('-pix_fmt yuv420p')
        //   // All device compatibility, Android in particular doesn't support higher profiles.
        //   .outputOptions('-profile:v baseline -level 3.0')
        //   // Quality 0 is lossless, 23 is default, and 51 is worst possible. 18-28 is a sane range.
        //   // .outputOptions('-crf 20')
        //   // Fast start, Moves some data to the beginning of the file, allowing the video to be played before it is completely downloaded.
        //   .outputOptions('-movflags +faststart')
        //   .outputOptions('-strict experimental')
        //   // compress file: ultrafast, superfast, veryfast, fast, medium, slow, slower, veryslow
        //   .outputOptions('-preset fast')
        //   // Faster processing Flag: -threads 0, Allow your CPU to use an optimal number of threads.
        //   .outputOptions('-threads 0')
        //   .on('end', () => resolve({
        //     fileName,
        //     toPath
        //   }))
        //   .on('error', reject)
        //   .toFormat('mp4');

        // if (options.size) {
        //   command.size(options.size);
        // }
        // // save to file
        // command.save(toPath);

        let outputOptions =
          '-vcodec libx264 -pix_fmt yuv420p -profile:v baseline -level 3.0 -movflags +faststart -strict experimental -preset fast -threads 0 -crf 23';
        if (options.size) {
          const sizes = options.size.split('x');
          const width = sizes[0];
          // retain aspect ratio just give height as -1 and it will automatically resize based on the width
          const height = '-1'; // sizes.length > 1 ? sizes[1] : '-1  ';
          outputOptions += ` -vf scale="${width}:${height}"`;
        }

        const q = `ffmpeg -i ${filePath} ${outputOptions} ${toPath}`;
        // use spawn, avoid exception buffer when it is more than 200KB
        const command = spawn(q, [], {
          shell: true,
          // stdin, stdout, stderr
          stdio: ['ignore', 'ignore', 'pipe']
        });
        let e = '';
        let duration = 0;
        let lastReportedProgress = 0;

        // Parse duration from initial ffmpeg output
        const parseDuration = (data: string): number => {
          const durationMatch = data.match(
            /Duration: (\d{2}):(\d{2}):(\d{2}\.\d{2})/
          );
          if (durationMatch) {
            const hours = parseInt(durationMatch[1], 10);
            const minutes = parseInt(durationMatch[2], 10);
            const seconds = parseFloat(durationMatch[3]);
            return hours * 3600 + minutes * 60 + seconds;
          }
          return 0;
        };

        // Parse progress from ffmpeg output
        const parseProgress = (data: string): IConvertProgress | null => {
          const timeMatch = data.match(/time=(\d{2}):(\d{2}):(\d{2}\.\d{2})/);
          const fpsMatch = data.match(/fps=\s*(\d+\.?\d*)/);
          const kbpsMatch = data.match(/bitrate=\s*(\d+\.?\d*)kbits\/s/);
          const sizeMatch = data.match(/size=\s*(\d+)kB/);

          if (timeMatch) {
            const hours = parseInt(timeMatch[1], 10);
            const minutes = parseInt(timeMatch[2], 10);
            const seconds = parseFloat(timeMatch[3]);
            const currentTimeSeconds = hours * 3600 + minutes * 60 + seconds;

            const percent =
              duration > 0
                ? Math.min((currentTimeSeconds / duration) * 100, 100)
                : 0;

            return {
              percent: Math.round(percent * 100) / 100,
              currentTime: timeMatch[0].replace('time=', ''),
              targetSize: sizeMatch ? `${sizeMatch[1]}kB` : '0kB',
              currentFps: fpsMatch ? parseFloat(fpsMatch[1]) : 0,
              currentKbps: kbpsMatch ? parseFloat(kbpsMatch[1]) : 0,
              targetTime:
                duration > 0
                  ? new Date(duration * 1000).toISOString().substring(11, 19)
                  : '00:00:00'
            };
          }
          return null;
        };

        command.stderr.on('data', (data) => {
          const dataStr = data.toString();
          e += dataStr;

          // Parse duration on first output
          if (duration === 0) {
            duration = parseDuration(dataStr);
          }

          // Parse and emit progress if callback is provided
          if (options.onProgress) {
            const progress = parseProgress(dataStr);
            if (progress) {
              // Only report progress every 4% to reduce websocket event frequency
              const currentProgress = Math.floor(progress.percent / 4) * 4;
              if (currentProgress > lastReportedProgress) {
                lastReportedProgress = currentProgress;
                options.onProgress(progress);
              }
            }
          }
        });

        command.on('exit', (code) => {
          if (!code) {
            // Emit 100% progress on completion
            if (options.onProgress) {
              options.onProgress({
                percent: 100,
                currentTime:
                  duration > 0
                    ? new Date(duration * 1000).toISOString().substring(11, 19)
                    : '00:00:00',
                targetSize: 'Complete',
                currentFps: 0,
                currentKbps: 0,
                targetTime:
                  duration > 0
                    ? new Date(duration * 1000).toISOString().substring(11, 19)
                    : '00:00:00'
              });
            }
            resolve({
              fileName,
              toPath
            });
            return;
          }
          reject(new Error(e));
        });
      });
    } catch (e) {
      throw new ConvertMp4ErrorException(e);
    }
  }

  public async getMetaData(filePath: string): Promise<any> {
    return new Promise((resolve, reject) => {
      ffmpeg.ffprobe(filePath, (err, metadata) => {
        if (err) {
          return reject(err);
        }
        return resolve(metadata);
      });
    });
  }

  public async createThumbs(
    filePath: string,
    options: {
      toFolder: string;
      count?: number;
      size?: string;
      onProgress?: (progress: IConvertProgress) => void;
    }
  ): Promise<string[]> {
    let thumbs: string[] = [];
    const totalCount = options.count || 3;
    const processedCount = 0;

    // eslint-disable-next-line new-cap
    return new Promise((resolve, reject) => {
      // eslint-disable-next-line new-cap
      new ffmpeg(filePath)
        .on('filenames', (filenames: string[]) => {
          thumbs = filenames;
        })
        .on('progress', (progress: any) => {
          // Emit raw progress for thumbnail creation (0-100% range)
          if (options.onProgress) {
            console.log(progress);
            // Calculate progress based on frames processed
            const thumbProgress = Math.min(
              (processedCount / totalCount) * 100,
              100
            );

            options.onProgress({
              percent: Math.round(thumbProgress),
              currentTime: progress.timemark || '00:00:00',
              targetTime: progress.timemark || '00:00:00',
              targetSize: 'Creating thumbnails...',
              currentFps: progress.currentFps || 0,
              currentKbps: progress.currentKbps || 0
            });
          }
        })
        .on('end', () => {
          // Emit 100% progress when thumbnail creation completes
          if (options.onProgress) {
            options.onProgress({
              percent: 100,
              currentTime: '00:00:00',
              targetTime: '00:00:00',
              targetSize: 'Thumbnails created',
              currentFps: 0,
              currentKbps: 0
            });
          }
          resolve(thumbs);
        })
        .on('error', reject)
        .screenshot({
          folder: options.toFolder,
          filename: `${StringHelper.randomString(5)}-%s.png`,
          count: totalCount,
          size: options.size || '480x?'
        });
    });
  }

  /**
   * check if this video support html5, we don't need to convert to h264 if any?
   * @param filePath
   * @returns
   */
  public async isSupportHtml5(filePath: string) {
    // get file name
    const ext = getExt(filePath);
    if (
      !ext ||
      !['.mp4', 'mp4', '.webm', 'webm', '.ogg', 'ogg'].includes(
        ext.toLocaleLowerCase()
      )
    )
      return false;

    const meta = await this.getMetaData(filePath);
    if (!meta?.streams?.length) return false;
    const videoStream = meta.streams.find((s) => s.codec_type === 'video');

    // TODO - check if pix_fmt: 'yuv420p'
    return ['h264', 'vp8'].includes(videoStream.codec_name);
  }
}
