## Overview


## License
This software is not free, do not copy or use if have no license.

## Setup

### API
1. Go to api folder, create `.env` file from `config-example > env > api.env`
2. Replace with our configuration
3. Run `yarn install` to install dependecies
Note: If you have issues with `sharp` module, run `yarn add sharp --ignore-engines` after run `yarn install` command
4. Run `yarn dev` for dev env or `yarn build && yarn start` from prod env

### User
1. Go to user folder, create `.env` file from `config-example > env > user.env`
2. Replace with our configuration
3. Run `yarn` to install dependecies
4. Run `yarn dev` for dev env or `yarn build && yarn start` from prod env

### Admin
1. Go to admin folder, create `.env` file from `config-example > env > admin.env`
2. Replace with our configuration
3. Run `yarn` to install dependecies
4. Run `yarn dev` for dev env or `yarn build && yarn start` from prod env

## Contact
- Adent.io <<EMAIL>>
