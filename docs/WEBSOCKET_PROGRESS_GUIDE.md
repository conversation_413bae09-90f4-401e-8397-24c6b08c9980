# WebSocket Progress Updates for Video Conversion

This guide explains how the WebSocket-based progress update system works between the main API server and the video-converter-api.

## Overview

The system enables real-time progress updates during video conversion by establishing WebSocket connections between:
1. **Main API Server** ↔ **Video Converter API** (for receiving progress)
2. **Main API Server** ↔ **Client/Frontend** (for forwarding progress)

## Architecture

```
[Client/Frontend] 
       ↕ WebSocket
[Main API Server] 
       ↕ WebSocket  
[Video Converter API]
       ↕ FFmpeg Process
```

## How It Works

### 1. Video Conversion Request Flow

1. **Client uploads video** to Main API Server
2. **Main API Server** creates WebSocket connection to Video Converter API
3. **Main API Server** uploads video to Video Converter API
4. **Main API Server** requests conversion with `socketId`
5. **Video Converter API** processes video and emits progress via WebSocket
6. **Main API Server** receives progress and forwards to client
7. **Video Converter API** completes conversion and notifies via WebSocket
8. **Main API Server** downloads converted video and cleans up

### 2. WebSocket Events

#### From Video Converter API to Main API Server:

- `video_conversion_progress`: Real-time progress updates
- `video_conversion_complete`: Conversion finished successfully  
- `video_conversion_error`: Conversion failed

#### Progress Data Structure:
```typescript
interface IConvertProgress {
  percent: number;        // 0-100
  currentTime: string;    // "00:01:30"
  targetSize: string;     // "1024kB"
  currentFps: number;     // 30
  currentKbps: number;    // 1000
  targetTime: string;     // "00:03:00"
}
```

## Implementation

### 1. Video Converter API (Server)

The video converter API has WebSocket support built-in:

```typescript
// video-converter-api/src/modules/socket/socket.gateway.ts
@WebSocketGateway({
  cors: { origin: '*' }
})
export class SocketGateway {
  emitToClient(clientId: string, event: string, data: any) {
    const client = this.clients.get(clientId);
    if (client) {
      client.emit(event, data);
    }
  }
}
```

### 2. Main API Server (Client)

The main API server connects as a WebSocket client:

```typescript
// api/src/modules/file/services/video-converter-client.service.ts
import { io, Socket } from 'socket.io-client';

private async createSocketConnection(
  socketId: string,
  onProgress?: (progress: IConvertProgress) => void
): Promise<Socket> {
  const socket = io(this.videoConverterSocketUrl, {
    transports: ['websocket', 'polling'],
    timeout: 10000,
    reconnection: false
  });

  socket.on('video_conversion_progress', (data) => {
    if (onProgress && data.progress) {
      onProgress(data.progress);
    }
  });

  return socket;
}
```

### 3. Frontend Integration

The frontend receives progress updates from the main API server:

```javascript
// Frontend WebSocket connection
const socket = io(API_ENDPOINT);

socket.on('file_progress_updated', (data) => {
  console.log(`Progress: ${data.progress.percent}%`);
  // Update UI progress bar
  updateProgressBar(data.progress.percent);
});
```

## Configuration

### Environment Variables

**Main API Server (.env):**
```bash
VIDEO_CONVERTER_API_URL=http://localhost:8083
```

**Video Converter API (.env):**
```bash
HTTP_PORT=8083
CORS_ORIGIN=*
```

### WebSocket URLs

The system automatically derives WebSocket URLs from HTTP URLs:
- HTTP: `http://localhost:8083` → WebSocket: `ws://localhost:8083`
- HTTPS: `https://api.example.com` → WebSocket: `wss://api.example.com`

## Testing

### 1. Test WebSocket Connection

```bash
cd video-converter-api
node test-websocket.js
```

### 2. Test Progress Updates

```bash
cd video-converter-api  
node test-progress.js
```

### 3. Test Integration

```bash
cd api
node test-video-converter-integration.js
```

## Error Handling

The system includes comprehensive error handling:

1. **WebSocket Connection Failures**: Falls back to HTTP-only mode
2. **Progress Update Failures**: Logs warnings but continues conversion
3. **Conversion Failures**: Emits error events via WebSocket
4. **Network Issues**: Automatic cleanup of connections

## Security Considerations

1. **CORS Configuration**: Properly configure CORS origins
2. **Authentication**: Add authentication for production WebSocket connections
3. **Rate Limiting**: Implement rate limiting for WebSocket connections
4. **Input Validation**: Validate all WebSocket message data

## Monitoring

Monitor the system using:

1. **Connection Logs**: Track WebSocket connections/disconnections
2. **Progress Metrics**: Monitor progress update frequency
3. **Error Rates**: Track conversion failure rates
4. **Performance**: Monitor conversion times and resource usage

## Troubleshooting

### Common Issues:

1. **WebSocket Connection Failed**
   - Check if video-converter-api is running
   - Verify CORS configuration
   - Check firewall/network settings

2. **No Progress Updates**
   - Verify socketId is passed correctly
   - Check WebSocket event listeners
   - Ensure FFmpeg is outputting progress data

3. **Connection Timeouts**
   - Increase WebSocket timeout settings
   - Check network stability
   - Verify server resources

## Production Deployment

For production deployment:

1. **Use HTTPS/WSS**: Enable SSL for WebSocket connections
2. **Load Balancing**: Configure sticky sessions for WebSocket connections
3. **Monitoring**: Set up comprehensive monitoring and alerting
4. **Scaling**: Consider horizontal scaling for video converter instances
5. **Cleanup**: Implement automatic cleanup of stale connections
