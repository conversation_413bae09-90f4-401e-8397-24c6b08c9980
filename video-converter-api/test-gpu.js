#!/usr/bin/env node

/**
 * Test script to verify GPU encoding capabilities
 * This script tests both GPU and CPU encoding fallback
 */

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

// Test if NVENC is available
function testNvencAvailability() {
  return new Promise((resolve) => {
    console.log('🔍 Testing NVENC availability...');
    
    const command = spawn('ffmpeg', ['-encoders'], {
      stdio: ['ignore', 'pipe', 'pipe']
    });

    let output = '';
    command.stdout.on('data', (data) => {
      output += data.toString();
    });

    command.on('exit', (code) => {
      const hasNvenc = output.includes('h264_nvenc');
      if (hasNvenc) {
        console.log('✅ NVENC encoder found');
      } else {
        console.log('❌ NVENC encoder not found');
      }
      resolve(hasNvenc);
    });
  });
}

// Test GPU encoding with a simple command
function testGpuEncoding() {
  return new Promise((resolve) => {
    console.log('🎬 Testing GPU encoding...');
    
    // Create a simple test video (1 second, solid color)
    const testCommand = [
      '-f', 'lavfi',
      '-i', 'testsrc=duration=1:size=320x240:rate=1',
      '-hwaccel', 'cuda',
      '-c:v', 'h264_nvenc',
      '-preset', 'p4',
      '-t', '1',
      '-y',
      '/tmp/test_gpu.mp4'
    ];

    const command = spawn('ffmpeg', testCommand, {
      stdio: ['ignore', 'pipe', 'pipe']
    });

    let errorOutput = '';
    command.stderr.on('data', (data) => {
      errorOutput += data.toString();
    });

    command.on('exit', (code) => {
      if (code === 0) {
        console.log('✅ GPU encoding test successful');
        // Clean up test file
        try {
          fs.unlinkSync('/tmp/test_gpu.mp4');
        } catch (e) {
          // Ignore cleanup errors
        }
        resolve(true);
      } else {
        console.log('❌ GPU encoding test failed');
        console.log('Error output:', errorOutput.slice(-500)); // Last 500 chars
        resolve(false);
      }
    });
  });
}

// Test CPU encoding fallback
function testCpuEncoding() {
  return new Promise((resolve) => {
    console.log('🖥️  Testing CPU encoding fallback...');
    
    // Create a simple test video (1 second, solid color)
    const testCommand = [
      '-f', 'lavfi',
      '-i', 'testsrc=duration=1:size=320x240:rate=1',
      '-c:v', 'libx264',
      '-preset', 'fast',
      '-t', '1',
      '-y',
      '/tmp/test_cpu.mp4'
    ];

    const command = spawn('ffmpeg', testCommand, {
      stdio: ['ignore', 'pipe', 'pipe']
    });

    let errorOutput = '';
    command.stderr.on('data', (data) => {
      errorOutput += data.toString();
    });

    command.on('exit', (code) => {
      if (code === 0) {
        console.log('✅ CPU encoding test successful');
        // Clean up test file
        try {
          fs.unlinkSync('/tmp/test_cpu.mp4');
        } catch (e) {
          // Ignore cleanup errors
        }
        resolve(true);
      } else {
        console.log('❌ CPU encoding test failed');
        console.log('Error output:', errorOutput.slice(-500)); // Last 500 chars
        resolve(false);
      }
    });
  });
}

// Check NVIDIA runtime
function checkNvidiaRuntime() {
  return new Promise((resolve) => {
    console.log('🐳 Checking NVIDIA Docker runtime...');
    
    const command = spawn('nvidia-smi', [], {
      stdio: ['ignore', 'pipe', 'pipe']
    });

    command.on('exit', (code) => {
      if (code === 0) {
        console.log('✅ NVIDIA runtime available');
        resolve(true);
      } else {
        console.log('❌ NVIDIA runtime not available');
        resolve(false);
      }
    });
  });
}

// Main test function
async function runTests() {
  console.log('🚀 Starting GPU encoding tests...\n');

  const nvidiaRuntime = await checkNvidiaRuntime();
  const nvencAvailable = await testNvencAvailability();
  const gpuEncoding = nvencAvailable ? await testGpuEncoding() : false;
  const cpuEncoding = await testCpuEncoding();

  console.log('\n📊 Test Results:');
  console.log('================');
  console.log(`NVIDIA Runtime: ${nvidiaRuntime ? '✅' : '❌'}`);
  console.log(`NVENC Available: ${nvencAvailable ? '✅' : '❌'}`);
  console.log(`GPU Encoding: ${gpuEncoding ? '✅' : '❌'}`);
  console.log(`CPU Encoding: ${cpuEncoding ? '✅' : '❌'}`);

  if (gpuEncoding) {
    console.log('\n🎉 GPU acceleration is working! Your video converter will use hardware encoding.');
  } else if (cpuEncoding) {
    console.log('\n⚠️  GPU acceleration not available, but CPU encoding works. Videos will be processed using CPU.');
  } else {
    console.log('\n💥 Both GPU and CPU encoding failed. Please check your FFmpeg installation.');
  }

  console.log('\n💡 Tips:');
  if (!nvidiaRuntime) {
    console.log('- Install nvidia-container-toolkit for GPU support');
  }
  if (!nvencAvailable) {
    console.log('- Make sure FFmpeg is compiled with NVENC support');
    console.log('- Verify your NVIDIA GPU supports NVENC');
  }
  if (!cpuEncoding) {
    console.log('- Check FFmpeg installation and libx264 codec availability');
  }
}

// Run the tests
runTests().catch(console.error);
