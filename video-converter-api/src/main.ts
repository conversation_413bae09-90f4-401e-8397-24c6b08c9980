require('dotenv').config();

import { NestFactory } from '@nestjs/core';
import { ValidationPipe } from '@nestjs/common';
import { NestExpressApplication } from '@nestjs/platform-express';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { AppModule } from './app.module';
import config from './config';

async function bootstrap() {
  const app = await NestFactory.create<NestExpressApplication>(AppModule);

  // Enable CORS
  app.enableCors(config.app.cors);

  // Global validation pipe
  app.useGlobalPipes(new ValidationPipe({
    transform: true,
    transformOptions: {
      enableImplicitConversion: true
    }
  }));

  // Disable x-powered-by header
  app.disable('x-powered-by');

  // Swagger documentation
  if (process.env.NODE_ENV === 'development') {
    const options = new DocumentBuilder()
      .setTitle('Video Converter API')
      .setDescription('Standalone video converter API server')
      .setVersion('1.0')
      .addTag('video-converter')
      .build();
    const document = SwaggerModule.createDocument(app, options);
    SwaggerModule.setup('api-docs', app, document);
  }
  // Get server from underlying platform
  const server = app.getHttpServer();
  server.setTimeout(600 * 1000); // 10 minutes timeout

  await app.listen(config.app.port);
  console.log(`Video Converter API is running on port ${config.app.port}`);
}

bootstrap();
