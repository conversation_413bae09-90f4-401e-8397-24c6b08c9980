import { Injectable } from '@nestjs/common';
import { spawn } from 'child_process';
import * as ffmpeg from 'fluent-ffmpeg';
import { join } from 'path';
import { StringHelper, getExt, ConvertMp4ErrorException } from '../../../shared';

export interface IConvertOptions {
  toPath?: string;
  size?: string; // https://github.com/fluent-ffmpeg/node-fluent-ffmpeg#video-frame-size-options
  onProgress?: (progress: IConvertProgress) => void;
}

export interface IConvertProgress {
  percent: number;
  currentTime: string;
  targetSize: string;
  currentFps: number;
  currentKbps: number;
  targetTime: string;
}

export interface IConvertResponse {
  fileName: string;
  toPath: string;
}

@Injectable()
export class VideoConverterService {
  public async convert2Mp4(
    filePath: string,
    options = {} as IConvertOptions
  ): Promise<IConvertResponse> {
    try {
      const fileName = `${StringHelper.randomString(5)}_${StringHelper.getFileName(filePath, true)}.mp4`;
      const toPath =
        options.toPath || join(StringHelper.getFilePath(filePath), fileName);

      // TODO - h264 just supports max 4k video

      return new Promise((resolve, reject) => {
        // Try GPU encoding first, fallback to CPU if GPU fails
        let outputOptions =
          '-c:v h264_nvenc -preset p6 -rc vbr -cq 28 -b:v 0 -pix_fmt yuv420p -movflags +faststart';

        if (options.size) {
          const sizes = options.size.split('x');
          const width = sizes[0];
          // retain aspect ratio just give height as -1 and it will automatically resize based on the width
          const height = '-1'; // sizes.length > 1 ? sizes[1] : '-1  ';
          outputOptions += ` -vf scale_cuda="${width}:${height}"`;
        }

        // First try GPU encoding
        const gpuCommand = `ffmpeg -hwaccel cuda -i ${filePath} ${outputOptions} ${toPath}`;

        const tryGpuEncoding = () => {
          return new Promise<void>((resolveGpu, rejectGpu) => {
            const command = spawn(gpuCommand, [], {
              shell: true,
              stdio: ['ignore', 'ignore', 'pipe']
            });

            let errorOutput = '';
            let duration = 0;

            command.stderr.on('data', (data) => {
              const message = data.toString();
              errorOutput += message;

              // Extract duration from ffmpeg output
              const durationMatch = message.match(/Duration: (\d{2}):(\d{2}):(\d{2})/);
              if (durationMatch) {
                const hours = parseInt(durationMatch[1], 10);
                const minutes = parseInt(durationMatch[2], 10);
                const seconds = parseInt(durationMatch[3], 10);
                duration = hours * 3600 + minutes * 60 + seconds;
              }

              // Extract progress information for GPU encoding
              const timeMatch = message.match(/time=(\d{2}):(\d{2}):(\d{2})/);
              const fpsMatch = message.match(/fps=\s*(\d+\.?\d*)/);
              const bitrateMatch = message.match(/bitrate=\s*(\d+\.?\d*)kbits\/s/);
              const sizeMatch = message.match(/size=\s*(\d+)kB/);

              if (timeMatch && options.onProgress) {
                const currentHours = parseInt(timeMatch[1], 10);
                const currentMinutes = parseInt(timeMatch[2], 10);
                const currentSeconds = parseInt(timeMatch[3], 10);
                const currentTime = currentHours * 3600 + currentMinutes * 60 + currentSeconds;

                const percent = duration > 0 ? Math.min((currentTime / duration) * 100, 99) : 0;

                options.onProgress({
                  percent: Math.round(percent),
                  currentTime: `${timeMatch[1]}:${timeMatch[2]}:${timeMatch[3]}`,
                  targetSize: sizeMatch ? `${sizeMatch[1]}kB` : 'Unknown',
                  currentFps: fpsMatch ? parseFloat(fpsMatch[1]) : 0,
                  currentKbps: bitrateMatch ? parseFloat(bitrateMatch[1]) : 0,
                  targetTime: duration > 0
                    ? new Date(duration * 1000).toISOString().substring(11, 19)
                    : '00:00:00'
                });
              }
            });

            command.on('exit', (code) => {
              if (code === 0) {
                // Emit 100% progress on successful GPU encoding completion
                if (options.onProgress) {
                  options.onProgress({
                    percent: 100,
                    currentTime:
                      duration > 0
                        ? new Date(duration * 1000).toISOString().substring(11, 19)
                        : '00:00:00',
                    targetSize: 'Complete',
                    currentFps: 0,
                    currentKbps: 0,
                    targetTime:
                      duration > 0
                        ? new Date(duration * 1000).toISOString().substring(11, 19)
                        : '00:00:00'
                  });
                }
                resolveGpu();
              } else {
                rejectGpu(new Error(`GPU encoding failed: ${errorOutput}`));
              }
            });
          });
        };

        const tryCpuEncoding = () => {
          return new Promise<void>((resolveCpu, rejectCpu) => {
            // Fallback to CPU encoding
            const cpuOutputOptions = '-vcodec libx264 -pix_fmt yuv420p -profile:v baseline -level 3.0 -movflags +faststart -strict experimental -preset fast -threads 0 -crf 23';
            let cpuOptions = cpuOutputOptions;

            if (options.size) {
              const sizes = options.size.split('x');
              const width = sizes[0];
              const height = '-1';
              cpuOptions += ` -vf scale="${width}:${height}"`;
            }

            const cpuCommand = `ffmpeg -i ${filePath} ${cpuOptions} ${toPath}`;
            const command = spawn(cpuCommand, [], {
              shell: true,
              stdio: ['ignore', 'ignore', 'pipe']
            });

            let e = '';
            let duration = 0;

            command.stderr.on('data', (data) => {
              const message = data.toString();
              e += message;

              // Extract duration from ffmpeg output
              const durationMatch = message.match(/Duration: (\d{2}):(\d{2}):(\d{2})/);
              if (durationMatch) {
                const hours = parseInt(durationMatch[1], 10);
                const minutes = parseInt(durationMatch[2], 10);
                const seconds = parseInt(durationMatch[3], 10);
                duration = hours * 3600 + minutes * 60 + seconds;
              }

              // Extract progress information
              const timeMatch = message.match(/time=(\d{2}):(\d{2}):(\d{2})/);
              const fpsMatch = message.match(/fps=\s*(\d+\.?\d*)/);
              const bitrateMatch = message.match(/bitrate=\s*(\d+\.?\d*)kbits\/s/);
              const sizeMatch = message.match(/size=\s*(\d+)kB/);

              if (timeMatch && options.onProgress) {
                const currentHours = parseInt(timeMatch[1], 10);
                const currentMinutes = parseInt(timeMatch[2], 10);
                const currentSeconds = parseInt(timeMatch[3], 10);
                const currentTime = currentHours * 3600 + currentMinutes * 60 + currentSeconds;

                const percent = duration > 0 ? Math.min((currentTime / duration) * 100, 99) : 0;

                options.onProgress({
                  percent: Math.round(percent),
                  currentTime: `${timeMatch[1]}:${timeMatch[2]}:${timeMatch[3]}`,
                  targetSize: sizeMatch ? `${sizeMatch[1]}kB` : 'Unknown',
                  currentFps: fpsMatch ? parseFloat(fpsMatch[1]) : 0,
                  currentKbps: bitrateMatch ? parseFloat(bitrateMatch[1]) : 0,
                  targetTime: duration > 0
                    ? new Date(duration * 1000).toISOString().substring(11, 19)
                    : '00:00:00'
                });
              }
            });

            command.on('exit', (code) => {
              if (!code) {
                // Emit 100% progress on completion
                if (options.onProgress) {
                  options.onProgress({
                    percent: 100,
                    currentTime:
                      duration > 0
                        ? new Date(duration * 1000).toISOString().substring(11, 19)
                        : '00:00:00',
                    targetSize: 'Complete',
                    currentFps: 0,
                    currentKbps: 0,
                    targetTime:
                      duration > 0
                        ? new Date(duration * 1000).toISOString().substring(11, 19)
                        : '00:00:00'
                  });
                }
                resolveCpu();
                return;
              }
              rejectCpu(new Error(e));
            });
          });
        };

        // Try GPU encoding first, fallback to CPU if it fails
        let gpuError: Error | null = null;

        tryGpuEncoding()
          .then(() => {
            resolve({
              fileName,
              toPath
            });
          })
          .catch((error) => {
            gpuError = error;
            console.warn('GPU encoding failed, falling back to CPU:', error.message);
            return tryCpuEncoding();
          })
          .then(() => {
            resolve({
              fileName,
              toPath
            });
          })
          .catch((cpuError) => {
            reject(new Error(`Both GPU and CPU encoding failed. GPU: ${gpuError?.message}, CPU: ${cpuError.message}`));
          });
      });
    } catch (e) {
      throw new ConvertMp4ErrorException(e);
    }
  }

  public async getMetaData(filePath: string): Promise<any> {
    return new Promise((resolve, reject) => {
      ffmpeg.ffprobe(filePath, (err, metadata) => {
        if (err) {
          return reject(err);
        }
        return resolve(metadata);
      });
    });
  }

  public async createThumbs(
    filePath: string,
    options: {
      toFolder: string;
      count?: number;
      size?: string;
    }
  ): Promise<string[]> {
    let thumbs = [];
    // eslint-disable-next-line new-cap
    return new Promise((resolve, reject) => {
      // eslint-disable-next-line new-cap
      new ffmpeg(filePath)
        .on('filenames', (filenames) => {
          thumbs = filenames;
        })
        .on('end', () => resolve(thumbs))
        .on('error', reject)
        .screenshot({
          folder: options.toFolder,
          filename: `${StringHelper.randomString(5)}-%s.png`,
          count: options.count || 3,
          size: options.size || '480x?'
        });
    });
  }

  /**
   * check if this video support html5, we don't need to convert to h264 if any?
   * @param filePath
   * @returns
   */
  public async isSupportHtml5(filePath: string) {
    // get file name
    const ext = getExt(filePath);
    if (
      !ext ||
      !['.mp4', 'mp4', '.webm', 'webm', '.ogg', 'ogg'].includes(
        ext.toLocaleLowerCase()
      )
    )
      return false;

    const meta = await this.getMetaData(filePath);
    if (!meta?.streams?.length) return false;
    const videoStream = meta.streams.find((s) => s.codec_type === 'video');

    // TODO - check if pix_fmt: 'yuv420p'
    return ['h264', 'vp8'].includes(videoStream.codec_name);
  }
}
