# Video Converter API

A standalone video converter API server built with NestJS that provides video conversion, thumbnail generation, and metadata extraction capabilities.

## Features

- **Video Conversion**: Convert videos to MP4 format with H.264 encoding
- **GPU Acceleration**: Hardware-accelerated encoding with NVIDIA NVENC (automatic fallback to CPU)
- **Real-time Progress**: WebSocket support for real-time conversion progress updates
- **Queue Processing**: Background video processing using Redis queues
- **Thumbnail Generation**: Create video thumbnails
- **Metadata Extraction**: Extract video metadata using FFprobe
- **HTML5 Support Check**: Check if video supports HTML5 playback
- **File Upload**: Upload videos for processing

## Installation

1. Install dependencies:
```bash
npm install
# or
yarn install
```

2. Copy environment configuration:
```bash
cp .env.example .env
```

3. Configure environment variables in `.env` file

4. Start the server:
```bash
# Development
npm run start:dev

# Production
npm run build
npm run start:prod
```

## API Endpoints

### Video Conversion

#### POST `/video-converter/convert`
Convert a video file to MP4 format.

**Request Body:**
```json
{
  "filePath": "/path/to/video.avi",
  "toPath": "/path/to/output.mp4",
  "size": "1280x720",
  "socketId": "socket-id-for-progress"
}
```

#### POST `/video-converter/convert/queue`
Queue a video for background conversion.

**Request Body:**
```json
{
  "filePath": "/path/to/video.avi",
  "toPath": "/path/to/output.mp4",
  "size": "1280x720",
  "socketId": "socket-id-for-progress"
}
```

### File Upload

#### POST `/video-converter/upload`
Upload a video file for processing.

**Form Data:**
- `video`: Video file

### Metadata

#### POST `/video-converter/metadata`
Get video metadata.

**Request Body:**
```json
{
  "filePath": "/path/to/video.mp4"
}
```

### Thumbnails

#### POST `/video-converter/thumbnails`
Generate video thumbnails.

**Request Body:**
```json
{
  "filePath": "/path/to/video.mp4",
  "toFolder": "/path/to/thumbnails",
  "count": 3,
  "size": "480x?"
}
```

### HTML5 Support

#### GET `/video-converter/check-html5/:filePath`
Check if video supports HTML5 playback.

## WebSocket Events

Connect to the WebSocket server to receive real-time updates:

### Events Emitted by Server:

- `video_conversion_progress`: Progress updates during conversion
- `video_conversion_complete`: Conversion completed successfully
- `video_conversion_error`: Conversion failed

### Events to Send to Server:

- `join-room`: Join a specific room for targeted updates

## Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `HTTP_PORT` | Server port | `8083` |
| `NODE_ENV` | Environment | `development` |
| `CORS_ORIGIN` | CORS origin | `*` |
| `REDIS_HOST` | Redis host | `127.0.0.1` |
| `REDIS_PORT` | Redis port | `6379` |
| `REDIS_DB` | Redis database | `0` |
| `REDIS_PASSWORD` | Redis password | `` |
| `REDIS_PREFIX` | Redis key prefix | `video_converter` |
| `UPLOAD_DIR` | Upload directory | `./uploads` |
| `TEMP_DIR` | Temporary directory | `./temp` |

## Dependencies

- **FFmpeg**: Required for video processing
- **Redis**: Required for queue processing
- **NVIDIA GPU**: Optional, for hardware-accelerated video encoding

## GPU Deployment

This server supports NVIDIA GPU acceleration for faster video encoding using NVENC. The service automatically falls back to CPU encoding if GPU encoding fails.

### Prerequisites for GPU Support

1. **NVIDIA GPU**: Compatible NVIDIA GPU with NVENC support
2. **NVIDIA Docker Runtime**: Install nvidia-container-toolkit
3. **NVIDIA Drivers**: Latest NVIDIA drivers installed on host

### Installing NVIDIA Docker Support

```bash
# Add NVIDIA package repositories
distribution=$(. /etc/os-release;echo $ID$VERSION_ID)
curl -s -L https://nvidia.github.io/nvidia-docker/gpgkey | sudo apt-key add -
curl -s -L https://nvidia.github.io/nvidia-docker/$distribution/nvidia-docker.list | sudo tee /etc/apt/sources.list.d/nvidia-docker.list

# Install nvidia-container-toolkit
sudo apt-get update && sudo apt-get install -y nvidia-container-toolkit

# Restart Docker daemon
sudo systemctl restart docker
```

### Running with GPU Support

#### Using Docker Compose (Recommended)
```bash
# The docker-compose.yml is already configured for GPU support
docker-compose up -d
```

#### Using Docker Run
```bash
# Build the image
docker build -t video-converter-api .

# Run with GPU support
docker run --gpus all \
  -p 8083:8083 \
  -e NVIDIA_VISIBLE_DEVICES=all \
  -e NVIDIA_DRIVER_CAPABILITIES=compute,video,utility \
  video-converter-api
```

### Verifying GPU Support

Check if GPU encoding is working:
```bash
# Test GPU capabilities (run this before deployment)
npm run test:gpu

# Inside the container, test NVENC availability
docker exec -it <container_name> ffmpeg -encoders | grep nvenc

# Check NVIDIA runtime in container
docker exec -it <container_name> nvidia-smi
```

### GPU Encoding Features

- **Hardware Acceleration**: Uses NVIDIA NVENC for H.264 encoding
- **Automatic Fallback**: Falls back to CPU encoding if GPU fails
- **Better Performance**: Significantly faster encoding on supported hardware
- **Lower CPU Usage**: Offloads encoding work to GPU

## Development

```bash
# Start in development mode
npm run start:dev

# Run tests
npm run test

# Build for production
npm run build
```

## API Documentation

When running in development mode, Swagger documentation is available at:
`http://localhost:8083/api-docs`
