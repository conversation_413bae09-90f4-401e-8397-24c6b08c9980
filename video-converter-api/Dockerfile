# Use NVIDIA CUDA base image with Ubuntu for GPU support
FROM nvidia/cuda:12.5.0-runtime-ubuntu22.04

# Install Node.js 18
RUN apt-get update && apt-get install -y \
    curl \
    gnupg \
    && curl -fsSL https://deb.nodesource.com/setup_20.x | bash - \
    && apt-get install -y nodejs

# Install FFmpeg with NVIDIA GPU support
RUN apt-get update && apt-get install -y \
    ffmpeg \
    && rm -rf /var/lib/apt/lists/*

# Install Yarn
RUN npm install -g yarn

# Create app directory
WORKDIR /usr/src/app

# Copy package files
COPY package*.json ./
COPY yarn.lock ./

# Install dependencies
RUN yarn install

# Copy source code
COPY . .

# Build the application
RUN yarn build

# Create uploads and temp directories
RUN mkdir -p uploads temp

# Expose port
EXPOSE 8083

# Start the application
CMD ["yarn", "start:prod"]
