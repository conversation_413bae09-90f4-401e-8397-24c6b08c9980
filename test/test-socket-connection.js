const { io } = require('socket.io-client');

// Test script to verify socket connection and ID handling
async function testSocketConnection() {
  console.log('Testing socket connection to video-converter-api...');
  
  const socket = io('http://localhost:3001', {
    transports: ['websocket', 'polling'],
    timeout: 10000,
    reconnection: false
  });

  socket.on('connect', () => {
    console.log(`✅ Connected to video converter WebSocket with ID: ${socket.id}`);
    
    // Test emitting to this specific socket ID
    console.log(`📤 Testing if we can receive messages on socket ID: ${socket.id}`);
    
    // Simulate what the video converter API would do
    setTimeout(() => {
      console.log('🔌 Disconnecting...');
      socket.disconnect();
    }, 2000);
  });

  socket.on('connect_error', (error) => {
    console.error('❌ Failed to connect to video converter WebSocket:', error);
  });

  socket.on('video_conversion_progress', (data) => {
    console.log('📊 Received progress update:', data);
  });

  socket.on('video_conversion_complete', (data) => {
    console.log('✅ Received completion event:', data);
  });

  socket.on('video_conversion_error', (data) => {
    console.error('❌ Received error event:', data);
  });

  socket.on('disconnect', () => {
    console.log('🔌 Disconnected from video converter WebSocket');
    process.exit(0);
  });
}

testSocketConnection().catch(console.error);
