server {
  listen 80 ;
	listen [::]:80 ;
	root /var/www/api.example.com/public;

	# Add index.php to the list if you are using PHP
	index index.html index.htm index.nginx-debian.html;
  server_name api.example.com;
  gzip on;
  gzip_proxied any;
  gzip_comp_level 4;
  gzip_types text/css application/javascript image/svg+xml;

  sendfile on;
  tcp_nopush on;
  tcp_nodelay on;
  keepalive_timeout 65;
  types_hash_max_size 2048;
  sendfile_max_chunk 512k;
  client_max_body_size 2000M;

  access_log off;
  error_log off;

	location / {
    try_files $uri @app;
  }

	location @app {
    proxy_set_header   X-Forwarded-For $remote_addr;
    proxy_set_header   Host $http_host;
    proxy_pass         http://localhost:8080;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection 'upgrade';
    proxy_cache_bypass $http_upgrade;

    # WebSocket support
    proxy_connect_timeout 1d;
    proxy_send_timeout 1d;
    proxy_read_timeout 1d;
	}

  location /.well-known {
    alias /var/www/api.example.com/public/.well-known;
  }

	location /videos/protected/ {
	  auth_request /authvideo;
	  root /var/www/api.example.com/public/;
	}

	location = /authvideo {
	  internal;
    set $query '';
    if ($request_uri ~* "[^\?]+\?(.*)$") {
      set $query $1;
    }
    proxy_pass http://localhost:8080/user/performer-assets/videos/auth/check?$query;
    proxy_pass_request_body   off;
    proxy_set_header Content-Length "";
	}
	
	location /photos/protected/ {
	  auth_request /authphoto;
	  root /var/www/api.example.com/public/;
	}
	
	location = /authphoto {
	  internal;
    set $query '';
    if ($request_uri ~* "[^\?]+\?(.*)$") {
      set $query $1;
    }
    proxy_pass http://localhost:8080/performer/performer-assets/photos/auth/check?$query;
    proxy_pass_request_body   off;
    proxy_set_header Content-Length "";
	}
	
	location /documents/ {
	  auth_request /authdocument;
	  root /var/www/api.example.com/public/;
	}
	
	location = /authdocument {
	  internal;
    set $query '';
    if ($request_uri ~* "[^\?]+\?(.*)$") {
      set $query $1;
    }
    proxy_pass http://localhost:8080/performers/documents/auth/check?$query;
    proxy_pass_request_body   off;
    proxy_set_header Content-Length "";
	}
	
	location /digital-products/protected {
	  auth_request /authproduct;
	  root /var/www/api.example.com/public/;
	}
	
	location = /authproduct {
	  internal;
    set $query '';
    if ($request_uri ~* "[^\?]+\?(.*)$") {
      set $query $1;
    }
    proxy_pass http://localhost:8080/user/performer-assets/products/auth/check?$query;
    proxy_pass_request_body   off;
    proxy_set_header Content-Length "";
	}
}