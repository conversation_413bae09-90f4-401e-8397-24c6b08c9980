server {
  listen 80;
	server_name example.com;

	root /var/www/example.com/public;

  gzip on;
  gzip_proxied any;
  gzip_comp_level 4;
  gzip_types text/css application/javascript image/svg+xml;

  sendfile on;
  tcp_nopush on;
  tcp_nodelay on;
  keepalive_timeout 65;
  types_hash_max_size 2048;
  sendfile_max_chunk 512k;
  
  # tuning performance for log request
  access_log off;
  error_log off;

  location / {
    try_files $uri @app;
  }

	location @app {
    proxy_pass http://localhost:8081;
    proxy_http_version 1.1;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection 'upgrade';
    proxy_set_header Host $host;
    proxy_cache_bypass $http_upgrade;
    proxy_redirect        off;
    proxy_set_header      Host $host;
    proxy_set_header      X-Real-IP $remote_addr;
    proxy_set_header      X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header      Proxy "";
  }

  location /.well-known {
    alias /var/www/example.com/public/.well-known;
  }

  location /_next/static/ {
    alias /var/www/example.com/.next/static/$1;
    access_log off;
    expires max;
  }

  location /static/ {
    alias /var/www/example.com/static/$1;
    expires max;
    autoindex off;
  }
}