server {
  listen 80;
	server_name admin.example.com;
	root /var/www/admin.example.com/public;

  gzip on;
  gzip_proxied any;
  gzip_comp_level 4;
  gzip_types text/css application/javascript image/svg+xml;

  sendfile on;
  tcp_nopush on;
  tcp_nodelay on;
  keepalive_timeout 65;
  types_hash_max_size 2048;
  sendfile_max_chunk 512k;

  access_log off;
  error_log off;

	location / {
    try_files $uri @app;
  }

	location @app {
    proxy_set_header   X-Forwarded-For $remote_addr;
    proxy_set_header   Host $http_host;
    proxy_pass         http://localhost:8082;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection 'upgrade';
    proxy_cache_bypass $http_upgrade;
  }

  location /.well-known {
    alias /var/www/admin.example.com/public/.well-known;
  }

  location /_next/static/ {
    alias /var/www/admin.example.com/.next/static/$1;
    access_log off;
    expires max;
  }

  location /static/ {
    alias /var/www/admin.example.com/static/$1;
    expires max;
    autoindex off;
  }
}