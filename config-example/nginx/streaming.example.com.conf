server {
  listen 80;
	server_name streaming.example.com;

	root /var/www/streaming.example.com;

  gzip on;
  gzip_proxied any;
  gzip_comp_level 4;
  gzip_types text/css application/javascript image/svg+xml;

  sendfile on;
  tcp_nopush on;
  tcp_nodelay on;
  keepalive_timeout 65;
  types_hash_max_size 2048;
  sendfile_max_chunk 512;
  
  # tuning performance for log request
  access_log off;
  error_log off;

	location / {
    proxy_pass http://localhost:5080;
    proxy_http_version 1.1;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header Host $host;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection "Upgrade";
  }
}